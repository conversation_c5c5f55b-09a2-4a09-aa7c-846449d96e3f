<?php

//decode by nige112
$iteace = "1";
if (is_file("../config.php")) {
	include "../config.php";
}
include "../api/wz.php";
if ($userdlzt == 1) {
	header("location: ./index.php");
	exit;
}
if ($user_zh == "" || $user_name == "" || $user_img == "" || $user_passid == "") {
	$userdlzt = "0";
} else {
	$userdlzt = "1";
	if ($user_zh == $glyadmin) {
		if ($user_passid != $passid) {
			exit("<script language=\"JavaScript\">;alert(\"账号信息异常,请重新登录!\");location.href=\"../index.php\";</script>;");
		} else {
			header("Location: ./index.php");
			exit;
		}
	} else {
		exit("<script language=\"JavaScript\">;alert(\"没有权限!\");location.href=\"../index.php\";</script>;");
	}
}
?><!DOCTYPE html>
<html lang="zh">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0, minimal-ui">
<meta name="keywords" content="<?php echo $name;?>">
<meta name="description" content="<?php echo $name . " ," . $subtitle;?>">
<meta name="author" content="<?php echo $name;?>">
<title>管理员登录 - <?php echo $name;?></title>
<link rel="shortcut icon" type="image/x-icon" href="<?php 
if (strpos($icon, "http") !== false) {
	echo $icon;
} else {
	echo "." . $icon;
}
?>">
<meta name="apple-mobile-web-app-capable" content="yes">
<meta name="apple-touch-fullscreen" content="yes">
<meta name="apple-mobile-web-app-status-bar-style" content="default">
<meta name="format-detection" content="telephone=no">
<meta http-equiv="x-rim-auto-match" content="none">
<link rel="stylesheet" type="text/css" href="./assets/css/materialdesignicons.min.css">
<link rel="stylesheet" type="text/css" href="./assets/css/bootstrap.min.css">
<link rel="stylesheet" type="text/css" href="./assets/css/animate.min.css">
<link rel="stylesheet" type="text/css" href="./assets/css/style.min.css">
<style>
.login-form .has-feedback {
    position: relative;
}
.login-form .has-feedback .form-control {
    padding-left: 36px;
}
.login-form .has-feedback .mdi {
    position: absolute;
    top: 0;
    left: 0;
    right: auto;
    width: 36px;
    height: 36px;
    line-height: 36px;
    z-index: 4;
    color: #dcdcdc;
    display: block;
    text-align: center;
    pointer-events: none;
}
.login-form .has-feedback.row .mdi {
    left: 15px;
}
</style>
</head>
  
<body class="center-vh">
<!--页面loading-->
<div id="lyear-preloader" class="loading">
  <div class="ctn-preloader">
    <div class="round_spinner">
      <div class="spinner"></div>
      <img src="<?php 
if (strpos($logo, "http") !== false) {
	echo $logo;
} else {
	echo "." . $logo;
}
?>" alt="">
    </div>
  </div>
</div>
<!--页面loading end-->
<div class="card card-shadowed p-5 w-420 mb-0 mr-2 ml-2">
  <div class="text-center mb-3">
    <a href="./login.php"> <img alt="light year admin" src="<?php 
if (strpos($logo, "http") !== false) {
	echo $logo;
} else {
	echo "." . $logo;
}
?>"> </a>
  </div>

  <form action="./api/login.php" method="post" class="login-form" autocomplete="off">
    <div class="form-group has-feedback">
      <span class="mdi mdi-account" aria-hidden="true"></span>
      <input type="text" class="form-control" id="username" placeholder="账号" name="username" required="required" minlength="5" maxlength="32">
    </div>

    <div class="form-group has-feedback">
      <span class="mdi mdi-lock" aria-hidden="true"></span>
      <input type="password" class="form-control" id="password" placeholder="密码" name="password" required="required" minlength="3" maxlength="16">
    </div>


    <div class="form-group">
      <button class="btn btn-block btn-primary" type="submit">立即登录</button>
    </div>
  </form>
  
  <p class="text-center text-muted mb-0">Copyright © <?php echo date("Y");?> <a href="<?php echo $_SERVER["REQUEST_SCHEME"] . "://" . $_SERVER["HTTP_HOST"];?>"><?php echo $glyname;?></a>. All right reserved</p>
</div>
  
<script type="text/javascript" src="./assets/js/jquery.min.js"></script>
<script type="text/javascript" src="./assets/js/main.min.js"></script>
<script type="text/javascript">;</script>
</body>
</html>