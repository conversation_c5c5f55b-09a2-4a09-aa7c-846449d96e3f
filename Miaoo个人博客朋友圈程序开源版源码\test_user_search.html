<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户搜索功能测试</title>
    <link rel="stylesheet" href="./assets/css/style.css">
    <link rel="stylesheet" href="https://at.alicdn.com/t/font_2040407_4u4xvjxy9aq.css">
</head>
<body>
    <div class="centent">
        <div class="sh-main">
            <div class="sh-main-head">
                <div class="sh-main-head-top" id="sh-main-head-top">
                    <div class="sh-main-head-top-left">
                        <h3 style="color: var(--textqh); margin-left: 20px;">用户搜索测试</h3>
                    </div>
                    <div class="sh-main-head-top-right">
                        <!-- 用户搜索按钮 -->
                        <div class="sh-main-head-top-right-s" onclick="kqUserSearch()">
                            <i class="iconfont icon-sousuo ri-sx" id="top-right-user-search"></i>
                        </div>
                    </div>
                </div>
                <div class="sh-main-head-img" style="background-color: #09c362; height: 200px;">
                </div>
            </div>
            
            <div style="padding: 20px; color: var(--textqh);">
                <h2>用户搜索与筛选功能说明</h2>
                <p>1. 点击右上角的搜索图标打开用户搜索弹窗</p>
                <p>2. 在搜索框中输入用户名或昵称进行模糊搜索</p>
                <p>3. 点击"筛选条件"可展开筛选选项</p>
                <p>4. 支持按地区、注册时间等条件筛选</p>
                <p>5. 支持多种排序方式</p>
                <p>6. 搜索结果会实时显示，支持防抖处理</p>
                <p>7. 点击搜索结果可跳转到用户个人页面</p>
                <br>
                <button onclick="kqUserSearch()" style="padding: 10px 20px; background: var(--theme); color: white; border: none; border-radius: 5px; cursor: pointer;">
                    测试用户搜索与筛选
                </button>
            </div>
        </div>
    </div>

    <!-- 用户搜索弹窗 -->
    <div class="user-search-modal" id="user-search-modal" onclick="gbUserSearch()">
        <div class="user-search-main" onclick="event.stopPropagation();">
            <!-- 顶部 -->
            <div class="user-search-main-top">
                <div class="user-search-main-top-title"><span>查找用户</span></div>
                <div class="user-search-main-top-close" onclick="gbUserSearch()">
                    <i class="iconfont icon-quxiao ri-sxhqx"></i>
                </div>
            </div>
            <!-- 搜索框 -->
            <div class="user-search-input-container">
                <input type="text" id="user-search-input" class="user-search-input"
                       placeholder="输入用户名或昵称进行搜索..."
                       autocomplete="off" maxlength="50"
                       oninput="searchUsers(this.value)">
                <i class="iconfont icon-sousuo user-search-icon"></i>
            </div>

            <!-- 筛选选项 -->
            <div class="user-search-filters">
                <div class="user-search-filters-title" onclick="toggleFilters()">
                    <span>筛选条件</span>
                    <span class="user-search-filters-toggle">
                        <i class="iconfont icon-xiala" id="filters-toggle-icon"></i>
                    </span>
                </div>
                <div class="user-search-filters-content" id="user-search-filters-content">
                    <!-- 地区筛选 -->
                    <div class="filter-group">
                        <label>地区筛选：</label>
                        <input type="text" id="filter-location" class="filter-input"
                               placeholder="如：西安、北京等"
                               oninput="applyFilters()">
                    </div>

                    <!-- 注册时间筛选 -->
                    <div class="filter-group">
                        <label>注册时间：</label>
                        <select id="filter-regtime" class="filter-select" onchange="applyFilters()">
                            <option value="">全部</option>
                            <option value="today">今天</option>
                            <option value="week">本周</option>
                            <option value="month">本月</option>
                            <option value="year">今年</option>
                        </select>
                    </div>

                    <!-- 排序方式 -->
                    <div class="filter-group">
                        <label>排序方式：</label>
                        <select id="filter-sort" class="filter-select" onchange="applyFilters()">
                            <option value="regtime_desc">注册时间(新到旧)</option>
                            <option value="regtime_asc">注册时间(旧到新)</option>
                            <option value="name_asc">昵称(A-Z)</option>
                            <option value="name_desc">昵称(Z-A)</option>
                        </select>
                    </div>

                    <!-- 清除筛选 -->
                    <div class="filter-actions">
                        <button class="filter-clear-btn" onclick="clearFilters()">清除筛选</button>
                    </div>
                </div>
            </div>
            <!-- 搜索结果 -->
            <div class="user-search-results" id="user-search-results">
                <div class="user-search-empty">
                    <i class="iconfont icon-yonghu1"></i>
                    <p>输入关键词搜索用户</p>
                </div>
            </div>
        </div>
    </div>

    <script src="./assets/js/jquery.min.js"></script>
    <script>
        // 用户搜索相关函数
        // 打开用户搜索弹窗
        function kqUserSearch() {
            if (document.getElementById("user-search-modal").style.display != "flex") {
                document.getElementById("user-search-modal").style.display = "flex";
                document.getElementById("user-search-input").focus();
                // 清空之前的搜索结果
                document.getElementById("user-search-results").innerHTML = '<div class="user-search-empty"><i class="iconfont icon-yonghu1"></i><p>输入关键词搜索用户</p></div>';
                document.getElementById("user-search-input").value = "";
                // 重置筛选条件
                clearFilters();
                // 默认收起筛选面板
                var filtersContent = document.getElementById("user-search-filters-content");
                var toggleIcon = document.getElementById("filters-toggle-icon");
                filtersContent.style.display = "none";
                toggleIcon.className = "iconfont icon-xiala";
            }
        }

        // 关闭用户搜索弹窗
        function gbUserSearch() {
            document.getElementById("user-search-modal").style.display = "none";
        }

        // 搜索用户函数
        let searchTimeout;
        function searchUsers(keyword) {
            // 清除之前的定时器
            if (searchTimeout) {
                clearTimeout(searchTimeout);
            }
            
            // 如果关键词为空，显示默认提示
            if (!keyword.trim()) {
                document.getElementById("user-search-results").innerHTML = '<div class="user-search-empty"><i class="iconfont icon-yonghu1"></i><p>输入关键词搜索用户</p></div>';
                return;
            }
            
            // 防抖处理，500ms后执行搜索
            searchTimeout = setTimeout(function() {
                performUserSearch(keyword.trim());
            }, 500);
        }

        // 执行用户搜索
        function performUserSearch(keyword) {
            // 显示加载状态
            document.getElementById("user-search-results").innerHTML = '<div class="user-search-loading"><i class="iconfont icon-jiazai"></i><p>搜索中...</p></div>';

            // 获取筛选条件
            var filterLocation = document.getElementById("filter-location").value;
            var filterRegtime = document.getElementById("filter-regtime").value;
            var filterSort = document.getElementById("filter-sort").value;

            // 构建请求参数
            var params = 'keyword=' + encodeURIComponent(keyword);
            if (filterLocation) {
                params += '&filter_location=' + encodeURIComponent(filterLocation);
            }
            if (filterRegtime) {
                params += '&filter_regtime=' + encodeURIComponent(filterRegtime);
            }
            if (filterSort) {
                params += '&filter_sort=' + encodeURIComponent(filterSort);
            }

            // 发送AJAX请求
            var xhr = new XMLHttpRequest();
            xhr.open('POST', './api/user_search.php');
            xhr.setRequestHeader("Content-type", "application/x-www-form-urlencoded");
            xhr.send(params);
            
            xhr.onreadystatechange = function() {
                if (xhr.readyState == 4 && xhr.status == 200) {
                    try {
                        var response = JSON.parse(xhr.responseText);
                        if (response[0].code == "200") {
                            displayUserSearchResults(response[0].data);
                        } else {
                            document.getElementById("user-search-results").innerHTML = '<div class="user-search-empty"><i class="iconfont icon-tanhao"></i><p>' + response[0].msg + '</p></div>';
                        }
                    } catch (e) {
                        document.getElementById("user-search-results").innerHTML = '<div class="user-search-empty"><i class="iconfont icon-tanhao"></i><p>搜索出错，请重试</p></div>';
                    }
                }
            };
        }

        // 显示用户搜索结果
        function displayUserSearchResults(users) {
            var resultsContainer = document.getElementById("user-search-results");
            
            if (users.length === 0) {
                resultsContainer.innerHTML = '<div class="user-search-empty"><i class="iconfont icon-wujieguo"></i><p>未找到相关用户</p></div>';
                return;
            }
            
            var html = '';
            users.forEach(function(user) {
                html += '<div class="user-search-item" onclick="goToUserProfile(\'' + user.username + '\')">';
                html += '    <div class="user-search-item-avatar">';
                html += '        <img src="' + user.img + '" alt="头像">';
                html += '    </div>';
                html += '    <div class="user-search-item-info">';
                html += '        <div class="user-search-item-name">' + user.name + '</div>';
                html += '        <div class="user-search-item-username">@' + user.username + '</div>';
                html += '        <div class="user-search-item-sign">' + user.sign + '</div>';
                html += '    </div>';
                html += '    <div class="user-search-item-arrow">';
                html += '        <i class="iconfont icon-youjiantou"></i>';
                html += '    </div>';
                html += '</div>';
            });
            
            resultsContainer.innerHTML = html;
        }

        // 跳转到用户个人页面
        function goToUserProfile(username) {
            alert('将跳转到用户 ' + username + ' 的个人页面');
            // 实际项目中这里会跳转到 archives.php
            // window.open('./archives.php?user=' + hashedUsername, '_blank');
        }

        // 阻止弹窗内部点击事件冒泡
        function hfljurl() {
            event.stopPropagation();
        }

        // 筛选功能相关函数
        // 切换筛选面板显示/隐藏
        function toggleFilters() {
            var filtersContent = document.getElementById("user-search-filters-content");
            var toggleIcon = document.getElementById("filters-toggle-icon");

            if (filtersContent.style.display === "none" || filtersContent.style.display === "") {
                filtersContent.style.display = "block";
                toggleIcon.className = "iconfont icon-shangla";
            } else {
                filtersContent.style.display = "none";
                toggleIcon.className = "iconfont icon-xiala";
            }
        }

        // 应用筛选条件
        function applyFilters() {
            var keyword = document.getElementById("user-search-input").value.trim();
            if (keyword) {
                // 清除之前的定时器
                if (searchTimeout) {
                    clearTimeout(searchTimeout);
                }
                // 立即执行搜索
                performUserSearch(keyword);
            }
        }

        // 清除所有筛选条件
        function clearFilters() {
            document.getElementById("filter-location").value = "";
            document.getElementById("filter-regtime").value = "";
            document.getElementById("filter-sort").value = "regtime_desc";

            // 如果有搜索关键词，重新搜索
            var keyword = document.getElementById("user-search-input").value.trim();
            if (keyword) {
                performUserSearch(keyword);
            }
        }
    </script>
</body>
</html>
