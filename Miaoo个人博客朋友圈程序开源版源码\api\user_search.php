<?php
//decode by nige112
header('Content-Type: application/json; charset=utf-8');

// 检查请求方法
if ($_SERVER["REQUEST_METHOD"] !== "POST") {
    $arr = [["code" => "201", "msg" => "非法请求"]];
    exit(json_encode($arr, JSON_UNESCAPED_UNICODE));
}

// 引入配置文件
if (!is_file("../config.php")) {
    $arr = [["code" => "201", "msg" => "配置文件不存在"]];
    exit(json_encode($arr, JSON_UNESCAPED_UNICODE));
}

require "../config.php";

// 获取搜索关键词
$keyword = isset($_POST["keyword"]) ? addslashes(htmlspecialchars($_POST["keyword"])) : "";

if (empty($keyword) || strlen(trim($keyword)) < 1) {
    $arr = [["code" => "201", "msg" => "搜索关键词不能为空"]];
    exit(json_encode($arr, JSON_UNESCAPED_UNICODE));
}

// 限制关键词长度，防止恶意请求
if (strlen($keyword) > 50) {
    $arr = [["code" => "201", "msg" => "搜索关键词过长"]];
    exit(json_encode($arr, JSON_UNESCAPED_UNICODE));
}

// 连接数据库
$conn = new mysqli($servername, $username, $password, $dbname);
if ($conn->connect_error) {
    $arr = [["code" => "201", "msg" => "数据库连接失败"]];
    exit(json_encode($arr, JSON_UNESCAPED_UNICODE));
}

// 搜索用户 - 按用户名和昵称进行模糊搜索，使用预处理语句防止SQL注入
$searchKeyword = "%{$keyword}%";
$sql = "SELECT id, username, name, img, sign, regtime FROM user
        WHERE ban = '0' AND (username LIKE ? OR name LIKE ?)
        ORDER BY regtime DESC
        LIMIT 20";

$stmt = $conn->prepare($sql);
if (!$stmt) {
    $arr = [["code" => "201", "msg" => "数据库查询准备失败"]];
    exit(json_encode($arr, JSON_UNESCAPED_UNICODE));
}

$stmt->bind_param("ss", $searchKeyword, $searchKeyword);
$stmt->execute();
$result = $stmt->get_result();

$users = [];
if ($result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        // 处理头像路径
        $userImg = $row["img"];
        if ($userImg == "./assets/img/tx.png") {
            $userImg = "./assets/img/tx.png";
        } else {
            // 如果是用户上传的头像，保持原路径
            if (strpos($userImg, "./user/headimg/") !== false) {
                $userImg = $userImg;
            }
        }
        
        // 格式化注册时间
        $regTime = $row["regtime"];
        if (is_numeric($regTime)) {
            $regTime = date('Y-m-d', $regTime);
        }
        
        $users[] = [
            "id" => $row["id"],
            "username" => $row["username"],
            "name" => $row["name"],
            "img" => $userImg,
            "sign" => $row["sign"] ?: "这里什么都没有哦!",
            "regtime" => $regTime
        ];
    }
}

// 关闭预处理语句和数据库连接
$stmt->close();
$conn->close();

// 返回搜索结果
if (count($users) > 0) {
    $arr = [["code" => "200", "msg" => "搜索成功", "data" => $users]];
} else {
    $arr = [["code" => "200", "msg" => "未找到相关用户", "data" => []]];
}

echo json_encode($arr, JSON_UNESCAPED_UNICODE);
?>
