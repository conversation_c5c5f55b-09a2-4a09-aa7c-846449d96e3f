<?php
//decode by nige112
header('Content-Type: application/json; charset=utf-8');

// 检查请求方法
if ($_SERVER["REQUEST_METHOD"] !== "POST") {
    $arr = [["code" => "201", "msg" => "非法请求"]];
    exit(json_encode($arr, JSON_UNESCAPED_UNICODE));
}

// 引入配置文件
if (!is_file("../config.php")) {
    $arr = [["code" => "201", "msg" => "配置文件不存在"]];
    exit(json_encode($arr, JSON_UNESCAPED_UNICODE));
}

require "../config.php";

// 获取搜索关键词和筛选参数
$keyword = isset($_POST["keyword"]) ? addslashes(htmlspecialchars($_POST["keyword"])) : "";
$filterLocation = isset($_POST["filter_location"]) ? addslashes(htmlspecialchars($_POST["filter_location"])) : "";
$filterRegtime = isset($_POST["filter_regtime"]) ? addslashes(htmlspecialchars($_POST["filter_regtime"])) : "";
$filterSort = isset($_POST["filter_sort"]) ? addslashes(htmlspecialchars($_POST["filter_sort"])) : "regtime_desc";

if (empty($keyword) || strlen(trim($keyword)) < 1) {
    $arr = [["code" => "201", "msg" => "搜索关键词不能为空"]];
    exit(json_encode($arr, JSON_UNESCAPED_UNICODE));
}

// 限制关键词长度，防止恶意请求
if (strlen($keyword) > 50) {
    $arr = [["code" => "201", "msg" => "搜索关键词过长"]];
    exit(json_encode($arr, JSON_UNESCAPED_UNICODE));
}

// 连接数据库
$conn = new mysqli($servername, $username, $password, $dbname);
if ($conn->connect_error) {
    $arr = [["code" => "201", "msg" => "数据库连接失败"]];
    exit(json_encode($arr, JSON_UNESCAPED_UNICODE));
}

// 构建搜索条件
$searchKeyword = "%{$keyword}%";
$whereConditions = ["ban = '0'", "(username LIKE ? OR name LIKE ?)"];
$params = [$searchKeyword, $searchKeyword];
$paramTypes = "ss";

// 添加地区筛选条件
if (!empty($filterLocation)) {
    // 通过用户的动态发布地址进行筛选
    $locationKeyword = "%{$filterLocation}%";
    $whereConditions[] = "username IN (SELECT DISTINCT ptpuser FROM essay WHERE ptpdw LIKE ?)";
    $params[] = $locationKeyword;
    $paramTypes .= "s";
}

// 添加注册时间筛选条件
if (!empty($filterRegtime)) {
    $currentTime = time();
    switch ($filterRegtime) {
        case 'today':
            $startTime = strtotime('today');
            $whereConditions[] = "regtime >= ?";
            $params[] = $startTime;
            $paramTypes .= "i";
            break;
        case 'week':
            $startTime = strtotime('-1 week');
            $whereConditions[] = "regtime >= ?";
            $params[] = $startTime;
            $paramTypes .= "i";
            break;
        case 'month':
            $startTime = strtotime('-1 month');
            $whereConditions[] = "regtime >= ?";
            $params[] = $startTime;
            $paramTypes .= "i";
            break;
        case 'year':
            $startTime = strtotime('-1 year');
            $whereConditions[] = "regtime >= ?";
            $params[] = $startTime;
            $paramTypes .= "i";
            break;
    }
}

// 构建排序条件
$orderBy = "regtime DESC";
switch ($filterSort) {
    case 'regtime_asc':
        $orderBy = "regtime ASC";
        break;
    case 'name_asc':
        $orderBy = "name ASC";
        break;
    case 'name_desc':
        $orderBy = "name DESC";
        break;
    default:
        $orderBy = "regtime DESC";
        break;
}

// 构建完整的SQL查询
$sql = "SELECT id, username, name, img, sign, regtime FROM user
        WHERE " . implode(" AND ", $whereConditions) . "
        ORDER BY {$orderBy}
        LIMIT 20";

$stmt = $conn->prepare($sql);
if (!$stmt) {
    $arr = [["code" => "201", "msg" => "数据库查询准备失败"]];
    exit(json_encode($arr, JSON_UNESCAPED_UNICODE));
}

// 绑定参数
if (!empty($params)) {
    $stmt->bind_param($paramTypes, ...$params);
}
$stmt->execute();
$result = $stmt->get_result();

$users = [];
if ($result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        // 处理头像路径
        $userImg = $row["img"];
        if ($userImg == "./assets/img/tx.png") {
            $userImg = "./assets/img/tx.png";
        } else {
            // 如果是用户上传的头像，保持原路径
            if (strpos($userImg, "./user/headimg/") !== false) {
                $userImg = $userImg;
            }
        }
        
        // 格式化注册时间
        $regTime = $row["regtime"];
        if (is_numeric($regTime)) {
            $regTime = date('Y-m-d', $regTime);
        }
        
        $users[] = [
            "id" => $row["id"],
            "username" => $row["username"],
            "name" => $row["name"],
            "img" => $userImg,
            "sign" => $row["sign"] ?: "这里什么都没有哦!",
            "regtime" => $regTime
        ];
    }
}

// 关闭预处理语句和数据库连接
$stmt->close();
$conn->close();

// 返回搜索结果
if (count($users) > 0) {
    $arr = [["code" => "200", "msg" => "搜索成功", "data" => $users]];
} else {
    $arr = [["code" => "200", "msg" => "未找到相关用户", "data" => []]];
}

echo json_encode($arr, JSON_UNESCAPED_UNICODE);
?>
