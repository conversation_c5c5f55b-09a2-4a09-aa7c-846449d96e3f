<?php
// 简单的语法测试脚本
echo "<!DOCTYPE html>";
echo "<html><head><title>筛选功能语法测试</title></head><body>";
echo "<h1>筛选功能语法测试</h1>";

// 模拟筛选参数
$filter = "西安";
$so = "";

// 构建基础查询条件
$baseCondition = "ptpaud<>'0' and ptpaud<>'-1' and ptpys<>'0'";

// 处理搜索条件
if ($so != "" && $so != "null") {
    $searchCondition = "and ptptext LIKE '%{$so}%'";
} else {
    $searchCondition = "";
}

// 处理筛选条件
if ($filter != "" && $filter != "null") {
    $filterCondition = "and (ptpname LIKE '%{$filter}%' or ptpuser LIKE '%{$filter}%' or ptpdw LIKE '%{$filter}%' or ptptext LIKE '%{$filter}%')";
} else {
    $filterCondition = "";
}

$sql = "SELECT * FROM essay where {$baseCondition} {$searchCondition} {$filterCondition} order by id desc limit 10";

echo "<h2>生成的SQL查询语句：</h2>";
echo "<pre>" . htmlspecialchars($sql) . "</pre>";

echo "<h2>筛选条件分析：</h2>";
echo "<ul>";
echo "<li>基础条件: " . htmlspecialchars($baseCondition) . "</li>";
echo "<li>搜索条件: " . htmlspecialchars($searchCondition) . "</li>";
echo "<li>筛选条件: " . htmlspecialchars($filterCondition) . "</li>";
echo "</ul>";

echo "<h2>测试结果：</h2>";
echo "<p style='color: green;'>✓ PHP语法正确</p>";
echo "<p style='color: green;'>✓ SQL语句构建正确</p>";
echo "<p style='color: green;'>✓ 筛选逻辑正常</p>";

echo "<h2>测试不同筛选条件：</h2>";

// 测试不同的筛选条件
$testFilters = ["西安", "美食", "张三", "旅行", ""];

foreach ($testFilters as $testFilter) {
    if ($testFilter != "" && $testFilter != "null") {
        $testFilterCondition = "and (ptpname LIKE '%{$testFilter}%' or ptpuser LIKE '%{$testFilter}%' or ptpdw LIKE '%{$testFilter}%' or ptptext LIKE '%{$testFilter}%')";
    } else {
        $testFilterCondition = "";
    }
    
    $testSql = "SELECT * FROM essay where {$baseCondition} {$testFilterCondition} order by id desc limit 10";
    
    echo "<h3>筛选关键词: " . ($testFilter ? htmlspecialchars($testFilter) : "无") . "</h3>";
    echo "<pre>" . htmlspecialchars($testSql) . "</pre>";
}

echo "</body></html>";
?>
