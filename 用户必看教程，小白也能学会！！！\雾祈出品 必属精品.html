<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="refresh" content="10;url=">
    <title>雾祈出品 必属精品</title>
    <style>
        body, html {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #1e1e2f, #2a2a40);
            color: #fff;
            overflow-x: hidden;
        }

        .container {
            width: 90%;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            text-align: center;
        }

        h1 {
            font-size: 3em;
            margin-bottom: 20px;
            animation: fadeIn 2s ease-in-out;
        }

        .box {
            background-color: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 40px;
            animation: slideUp 1.5s ease-in-out;
        }

        .box ol {
            text-align: left;
            padding-left: 20px;
        }

        .box ol li {
            margin-bottom: 15px;
            line-height: 1.6;
        }

        .footer-links {
            margin-top: 40px;
            display: flex;
            justify-content: center;
            gap: 20px;
            flex-wrap: wrap;
        }

        .footer-links .link-card {
            background-color: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 12px;
            width: 300px;
            text-align: center;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }

        .footer-links .link-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
        }

        .footer-links .link-card a {
            color: #fff;
            text-decoration: none;
            font-weight: bold;
            display: block;
            margin-bottom: 10px;
            padding: 10px 20px;
            border: 2px solid #da78c5;
            border-radius: 5px;
            transition: all 0.3s ease;
        }

        .footer-links .link-card a:hover {
            background-color: #da78c5;
            color: #fff;
        }

        .footer-links .link-card p {
            color: #ddd;
            font-size: 0.9em;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes slideUp {
            from { transform: translateY(20px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }

        .social-links {
            margin-top: 40px;
            display: flex;
            justify-content: center;
            gap: 20px;
        }

        .social-links a {
            color: #fff;
            font-size: 1.5em;
            transition: color 0.3s ease;
        }

        .social-links a:hover {
            color: #da78c5;
        }
    </style>
    <script>
        var _countAA = 0;

        function doBBBd() {}

        doBBBd();

        document.addEventListener('keyup', function() {
            _countAA -= 10;
            doBBBd();
        }, false);

        document.addEventListener('click', function() {
            _countAA -= 10;
            doBBBd();
        }, false);
    </script>
</head>

<body>
    <div class="container">
        <h1>雾祈出品 必属精品</h1>
        <div class="box">
          
            <ol>
                <li>本站提供的所有源码、软件及学习资料等资源仅供研究学习和个人参考之用，请勿用于商业用途或任何违法活动。如果您发现我们的内容侵犯了您的版权或其他权益，请立即联系我们，我们将迅速采取措施，移除相关侵权内容。</li>
                <br>
                <li>用户需知悉，对于提供下载的软件和程序代码，本站并不拥有其所有权，这些资源的版权归其合法持有者所有。雾祈资源网仅作为一个学习交流平台存在。</li>
                <br>
                <li>我们所提供的资源仅供学习与研究使用，并且请您在下载后24小时内自行删除。任何关于这些资源的商业行为或违法行为均与雾祈资源网无关。</li>
                <br>
                <li>请用户遵守相关法律法规，合理使用本站资源。如果因违反法律法规而产生的一切后果由用户自行承担。</li>
                <br>
                <li>雾祈资源网保留对本声明的最终解释权，并有权根据实际情况修改声明内容。</li>
            </ol>
        </div>
        <div class="footer-links">
            <div class="link-card">
                <a href="https://mistora.cc">雾祈官网</a>
                <p>访问我们的官方网站，了解更多关于雾祈资源网的信息</p>
            </div>
            <div class="link-card">
                <a href="https://zcnw4inahhn0.feishu.cn/docx/ExKFdHdxboZV0Axsn7GcINZynRc?from=from_copylink">飞书（业务）</a>
                <p>获取最新的站长业务、跟着站长赚大钱，当人上人</p>
            </div>
	<div class="link-card">
                <a href="https://bkm.mistora.cc/">博客喵</a>
                <p>上百款源码与资源，亲测搭建无问题，低价便宜</p>
            </div>
            <div class="link-card">
                <a href="https://idc.mistora.cc/">拾光云</a>
                <p>特价服务器，优惠活动，买服务器可包搭建网站！</p>
            </div>
	<div class="link-card">
                <a href="https://mzf.mistora.cc/">码支付</a>
                <p>使用我的服务器就送码支付套餐，用多久送多久</p>
            </div>
	<div class="link-card">
                <a href="https://slm.mistora.cc/">收录喵</a>
                <p>用心精选每一个链接，不定时上传网站更新</p>
            </div>
        </div>
        <div class="social-links">
            <a href="https://twitter.com/mistora" target="_blank" rel="noopener noreferrer">&#x1F426;</a> <!-- Twitter -->
            <a href="https://facebook.com/mistora" target="_blank" rel="noopener noreferrer">&#x1F4F1;</a> <!-- Facebook -->
            <a href="https://instagram.com/mistora" target="_blank" rel="noopener noreferrer">&#x1F4F7;</a> <!-- Instagram -->
        </div>
    </div>
</body>

</html>