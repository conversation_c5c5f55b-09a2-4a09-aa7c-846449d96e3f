<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>内容筛选功能测试</title>
    <link rel="stylesheet" href="./assets/css/style.css">
    <link rel="stylesheet" href="https://at.alicdn.com/t/font_2040407_4u4xvjxy9aq.css">
</head>
<body>
    <div class="centent">
        <div class="sh-main">
            <div class="sh-main-head">
                <div class="sh-main-head-top" id="sh-main-head-top">
                    <div class="sh-main-head-top-left">
                        <h3 style="color: var(--textqh); margin-left: 20px;">内容筛选测试</h3>
                    </div>
                    <div class="sh-main-head-top-right">
                        <!-- 用户搜索按钮 -->
                        <div class="sh-main-head-top-right-s" onclick="kqUserSearch()">
                            <i class="iconfont icon-sousuo ri-sx" id="top-right-user-search"></i>
                        </div>
                        
                        <!-- 内容筛选按钮 -->
                        <div class="sh-main-head-top-right-s" onclick="kqContentFilter()">
                            <i class="iconfont icon-shaixuan ri-sx" id="top-right-content-filter"></i>
                        </div>
                    </div>
                </div>
                <div class="sh-main-head-img" style="background-color: #09c362; height: 200px;">
                </div>
            </div>
            
            <!-- 筛选状态显示 -->
            <div class="content-filter-status" id="content-filter-status" style="display: flex;">
                <div class="filter-status-content">
                    <i class="iconfont icon-shaixuan"></i>
                    <span>当前筛选：西安</span>
                    <div class="filter-clear-btn" onclick="clearContentFilter()">
                        <i class="iconfont icon-quxiao"></i>
                    </div>
                </div>
            </div>
            
            <div style="padding: 20px; color: var(--textqh);">
                <h2>内容筛选功能说明</h2>
                <p>1. 点击右上角的筛选图标打开内容筛选弹窗</p>
                <p>2. 输入关键词（如：西安、美食、旅行等）</p>
                <p>3. 支持筛选用户昵称、用户名、发布地点、动态内容</p>
                <p>4. 筛选后只显示包含关键词的动态内容</p>
                <p>5. 可以使用快捷标签或清除筛选</p>
                <br>
                <button onclick="kqContentFilter()" style="padding: 10px 20px; background: var(--theme); color: white; border: none; border-radius: 5px; cursor: pointer; margin-right: 10px;">
                    测试内容筛选
                </button>
                <button onclick="testFilter('西安')" style="padding: 10px 20px; background: #ff6b35; color: white; border: none; border-radius: 5px; cursor: pointer; margin-right: 10px;">
                    筛选"西安"
                </button>
                <button onclick="testFilter('美食')" style="padding: 10px 20px; background: #f7931e; color: white; border: none; border-radius: 5px; cursor: pointer; margin-right: 10px;">
                    筛选"美食"
                </button>
                <button onclick="clearContentFilter()" style="padding: 10px 20px; background: #6c757d; color: white; border: none; border-radius: 5px; cursor: pointer;">
                    清除筛选
                </button>
            </div>
        </div>
    </div>

    <!-- 内容筛选弹窗 -->
    <div class="content-filter-modal" id="content-filter-modal" onclick="gbContentFilter()">
        <div class="content-filter-main" onclick="event.stopPropagation();">
            <!-- 顶部 -->
            <div class="content-filter-main-top">
                <div class="content-filter-main-top-title"><span>筛选内容</span></div>
                <div class="content-filter-main-top-close" onclick="gbContentFilter()">
                    <i class="iconfont icon-quxiao ri-sxhqx"></i>
                </div>
            </div>
            <!-- 筛选说明 -->
            <div class="content-filter-description">
                <p>输入关键词筛选动态内容，支持按用户昵称、用户名、发布地点、动态内容进行筛选</p>
            </div>
            <!-- 筛选输入框 -->
            <div class="content-filter-input-container">
                <input type="text" id="content-filter-input" class="content-filter-input" 
                       placeholder="例如：西安、张三、美食..." 
                       autocomplete="off" maxlength="50">
                <i class="iconfont icon-shaixuan content-filter-icon"></i>
            </div>
            <!-- 快捷筛选标签 -->
            <div class="content-filter-tags">
                <div class="content-filter-tags-title">快捷筛选：</div>
                <div class="content-filter-tags-list">
                    <span class="filter-tag" onclick="setFilterKeyword('西安')">西安</span>
                    <span class="filter-tag" onclick="setFilterKeyword('北京')">北京</span>
                    <span class="filter-tag" onclick="setFilterKeyword('上海')">上海</span>
                    <span class="filter-tag" onclick="setFilterKeyword('广州')">广州</span>
                    <span class="filter-tag" onclick="setFilterKeyword('深圳')">深圳</span>
                    <span class="filter-tag" onclick="setFilterKeyword('美食')">美食</span>
                    <span class="filter-tag" onclick="setFilterKeyword('旅行')">旅行</span>
                    <span class="filter-tag" onclick="setFilterKeyword('生活')">生活</span>
                </div>
            </div>
            <!-- 操作按钮 -->
            <div class="content-filter-actions">
                <button class="filter-clear-button" onclick="clearContentFilter()">清除筛选</button>
                <button class="filter-apply-button" onclick="applyContentFilter()">应用筛选</button>
            </div>
        </div>
    </div>

    <script src="./assets/js/jquery.min.js"></script>
    <script>
        // 内容筛选相关函数
        // 打开内容筛选弹窗
        function kqContentFilter() {
            if (document.getElementById("content-filter-modal").style.display != "flex") {
                document.getElementById("content-filter-modal").style.display = "flex";
                document.getElementById("content-filter-input").focus();
                
                // 如果当前有筛选条件，显示在输入框中
                var currentFilter = getUrlParameter('filter');
                if (currentFilter && currentFilter !== 'null') {
                    document.getElementById("content-filter-input").value = decodeURIComponent(currentFilter);
                }
            }
        }

        // 关闭内容筛选弹窗
        function gbContentFilter() {
            document.getElementById("content-filter-modal").style.display = "none";
        }

        // 设置筛选关键词（快捷标签点击）
        function setFilterKeyword(keyword) {
            document.getElementById("content-filter-input").value = keyword;
        }

        // 应用内容筛选
        function applyContentFilter() {
            var filterKeyword = document.getElementById("content-filter-input").value.trim();
            
            if (filterKeyword === "") {
                alert("请输入筛选关键词");
                return;
            }
            
            alert('将应用筛选条件：' + filterKeyword);
            gbContentFilter();
            
            // 更新筛选状态显示
            updateFilterStatus(filterKeyword);
        }

        // 清除内容筛选
        function clearContentFilter() {
            alert('将清除所有筛选条件');
            document.getElementById("content-filter-status").style.display = "none";
        }

        // 测试筛选功能
        function testFilter(keyword) {
            alert('将筛选包含"' + keyword + '"的内容');
            updateFilterStatus(keyword);
        }

        // 更新筛选状态显示
        function updateFilterStatus(keyword) {
            var statusElement = document.getElementById("content-filter-status");
            var spanElement = statusElement.querySelector("span");
            spanElement.textContent = "当前筛选：" + keyword;
            statusElement.style.display = "flex";
        }

        // 获取URL参数的辅助函数
        function getUrlParameter(name) {
            name = name.replace(/[\[]/, '\\[').replace(/[\]]/, '\\]');
            var regex = new RegExp('[\\?&]' + name + '=([^&#]*)');
            var results = regex.exec(location.search);
            return results === null ? '' : decodeURIComponent(results[1].replace(/\+/g, ' '));
        }

        // 阻止弹窗内部点击事件冒泡
        function hfljurl() {
            event.stopPropagation();
        }
    </script>
</body>
</html>
