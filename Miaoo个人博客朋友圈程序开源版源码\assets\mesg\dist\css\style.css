@font-face {
    font-family: "iconfont"; /* Project id 3852781 */
    src: url('../font/font_3852781_19ai98qdctc.ttf') format('woff2'),
         url('../font/font_3852781_19ai98qdctc.woff') format('woff'),
         url('../font/font_3852781_19ai98qdctc.woff2') format('truetype');
  }
  
  .iconfont-sh {
    font-family: "iconfont" !important;
    font-size: 16px;
    font-style: normal;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
  
  .icon-zhengque:before {
    content: "\e70e";
  }
  
  .icon-cuowu:before {
    content: "\e70d";
  }
  
  .icon-xinxi:before {
    content: "\e6bc";
  }


/* 外部框架用于定位 所有提示元素都将显示在这个元素内 */
.suhua-notyn-carrier{
    position: fixed;
    bottom: 90px;
    right: 0;
    max-width: 320px;
    z-index: 99999999;
    display: flex;
    flex-direction: column;
    align-items: end;
}

/* 提示信息元素框架 */
.notyn{
    min-width: 150px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    margin-top: 10px;
    padding: 14px 15px 14px 15px;
    box-shadow: -2px 2px 5px rgb(0,0,0,0.10);
    color:rgba(255, 255, 255, 1);
    backdrop-filter: saturate(180%) blur(20px);
    -webkit-backdrop-filter: saturate(180%) blur(20px);
}

/* icon图标样式 */
.notyn-icon{
    /* height: 21px;
    width: 21px;
    background: #fff; */
    border-radius: 50%;
    display: block;
    position: relative;
    font-size: 21px;
    pointer-events: none;
}



/* 加载中提示专用增加属性 加上这个属性后 加载提示弹窗将会与 其他3个弹窗重叠,不会独占一行*/
.notynload{
    position:absolute;
    bottom: 0;
    right: 0;
}
/* 转圈动画标志图标 */
.sh-notyn-load{
    font-size: 16px;
    animation: fadenum 1s infinite;
    pointer-events: none;
    display: flex;
    justify-content: center;
    align-items: center;
}
/* 加载中转圈伪元素 */
.sh-notyn-load::before{
    content: "";
    width: 16px;
    height: 16px;
    display: inline-block;
    border: .1em solid transparent;
    border-radius: 50%;
    border-top-color: #ffffff;
    border-bottom-color: #ffffff;
    font-size: 25px;
    margin: auto;
    box-sizing: border-box;
}
/* icon里的伪元素 */
/*.notyn-icon:before {
    content: "";
    background: #48b366;
    display: block;
    position: absolute;
    width: 3px;
    border-radius: 3px;
    height: 11px;
    -webkit-transform: rotate(
45deg);
    transform: rotate(
45deg);
    top: 5px;
    left: 10px;
}
/* icon里的伪元素2 */
/*.notyn-icon:after {
    content: "";
    background: #3dc763;
    display: block;
    position: absolute;
    width: 3px;
    border-radius: 3px;
    height: 6px;
    -webkit-transform: rotate(
-45deg);
    transform: rotate(
-45deg);
    top: 9px;
    left: 6px;
}*/

/* 提示的文字样式 */
.notyn-span{
    margin: 0px 10px;
    color: rgba(255, 255, 255);
    font-size: 14px;
    pointer-events: none;
}




/* 全屏遮罩样式 */
.suhua-notyn-carrier-mask{
    width: 100%;
    height: 100%;
    display: none;
    position:fixed;
    bottom:0;
    right:0;
    background: rgba(0, 0, 0, 0);
    z-index: 99999998;
}





 















/* 动画区域 */

/* 入场动画调用 */
.sh_move_open{
    -webkit-animation:sh_dh_move_open 0.6s;
    animation-fill-mode:forwards;
}
/* 退场动画调用 */ 
.sh_move_close{
    -webkit-animation:sh_dh_move_close 0.20s;
    animation-fill-mode:forwards;
}
/* 入场动画代码 请使用动画名调用*/
@-webkit-keyframes sh_dh_move_open{
    0% {
        -webkit-transform:translateX(100%);
      }
      50%{
          -webkit-transform:translateX(0);
          
      }
      70%{
          -webkit-transform:translateX(0.8px);
      }
      80%{
           -webkit-transform:translateX(0.4px);
      }
      90%,100%{
          -webkit-transform:translateX(0);
      }
}
/* 退场动画代码 请使用动画名调用*/
@-webkit-keyframes sh_dh_move_close{
    0%{ -webkit-transform:translateX(0px); }
    100%{
        -webkit-transform:translateX(100%);
    }
}


/* 加载提示转圈动画 */
@keyframes fadenum{
    100%{transform:rotate(360deg);}
}


/* 加载提示退出动画调用 */ 
.sh_move_loadout_close{
    -webkit-animation:sh_dh_move_loadout_close 0.1s;
    animation-fill-mode:forwards;
}
/* 加载提示退出动画 请使用动画名调用*/
@-webkit-keyframes sh_dh_move_loadout_close{
    0%{ -webkit-transform:translateX(0px); }
    100%{
        -webkit-transform:translateX(1000%);
    }
}