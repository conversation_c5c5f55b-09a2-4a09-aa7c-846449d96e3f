*{
    margin: 0;
    padding: 0;
}

/*定义颜色组*/
:root{
    --theme:#09c362;/*主题绿色*/
    --themetm:rgb(7,193,96,0.1);/*带透明主题绿色*/
    --thetitle:#586c97;/*主题文本颜色 暗蓝*/
    --protext:rgb(255,85,33);/*提示文本颜色 橙色*/
    --protextbg:rgb(255,85,33,0.1);/*提示文本背景颜色 淡橙色*/
    --backbg:rgb(245 245 245);/*背景颜色 淡灰色*/
    --cobg:rgb(255 255 255);/*背景颜色或文本色 白色*/
    --bganh:#4c5254;/*暗黑色*/
    --textqh:#282828;/*浅黑色字体颜色*/
    --textbs:rgb(255 255 255);/*白色字体颜色*/
    --texths:rgb(120 120 120);/*灰色字体颜色*/
    --fgxys:#f2f2f2;/*灰色分割线颜色*/
    --iconhs:rgb(120 120 120);/*灰色icon*/
    --iconbs:rgb(255 255 255);/*白色icon*/
    --bodys:#f0f0f0;/*body背景色*/
    --dzplcdfg:#383e41;/*点赞评论菜单分割线*/
    --dbztlys:rgba(240, 240, 240, 0.8);/*顶部状态栏 白色*/
    --dbztlysh:rgba(240, 240, 240, 0);/*顶部状态栏 透明*/
    --dbztlyshs:rgba(52, 52, 52, 0.8);/*顶部状态栏 黑色*/
    --gdwys:rgb(195 195 195);/*滚动条颜色*/
    --zsgfh:rgb(42,41,41,0.10);
    --imgbgy:rgb(245,245,245,0.60);
    --menu:rgb(200,200,200,0.40);/*右下角悬浮菜单*/
    --adgg:rgb(175,175,175);
}


/*夜间模式*/
/*定义颜色组*/
.dark-theme{
    --theme:#09c362;
    --themetm:rgb(7,193,96,0.1);
    --thetitle:#586c97;
    --protext:rgb(255,85,33);
    --protextbg:rgb(255,85,33,0.1);
    --backbg:rgb(62 62 62);
    --cobg:#323335;
    --bganh:#4c5254;
    --textqh:#ffffff;
    --textbs:rgb(255 255 255);
    --texths:rgb(136 136 136);
    --fgxys:#404040;
    --iconhs:rgb(255 255 255);
    --iconbs:rgb(255 255 255);
    --bodys:#424242;
    --dzplcdfg:#383e41;
    --dbztlys:rgb(66,66,66,0.8);
    --dbztlysh:rgba(240, 240, 240, 0);
    --dbztlyshs:rgba(52, 52, 52, 0.8);
    --gdwys:rgb(112 112 112);
    --zsgfh:rgb(124,124,124,0.2);
    --imgbgy:rgb(70,70,70,0.40);
    --menu:rgb(100,100,100,0.40);
    --adgg:rgb(116,116,116);
}

body{
    background: var(--bodys);
    background-attachment: fixed;
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center;
}
/*设置滚动条宽度*/
body::-webkit-scrollbar {
        width:4px;
    }
/*设置外层滑动槽的颜色*/
body::-webkit-scrollbar-track {
    background-color: rgb(0,0,0,0);
}
/*设置滑动条的颜色*/
body::-webkit-scrollbar-thumb {
    background-color: var(--gdwys);
    border-radius: 4px;
}

html, body {
    height: 100%;
}

a{
	-webkit-tap-highlight-color: rgba(255, 255, 255, 0);
    -webkit-user-select:none;
	-moz-user-focus: none;
    -moz-user-select: none;
    text-decoration:none;
    outline: none;
    color:var(--cobg);
}
img {
    -webkit-user-drag: none;
    -webkit-tap-highlight-color: rgba(255, 255, 255, 0);
    -webkit-user-select:none;
	-moz-user-focus: none;
    -moz-user-select: none;
    /*animation:myfirsjsimgtm 2s;
    -webkit-animation:myfirsjsimgtm 2s; /* Safari and Chrome */
}

/*图片由模糊到清晰动画*/
@keyframes myfirsjsimgtm
{
    0% {
        /*opacity:0;*/
        filter: blur(10px);
      }

      100%{
          /*opacity:1;*/
          filter: blur(0px);
      }
}





div{
    -webkit-tap-highlight-color: rgba(255, 255, 255, 0);
    -webkit-user-select:none;
	-moz-user-focus: none;
    -moz-user-select: none;
}

ul {
    list-style:none;
}
input:-webkit-autofill {-webkit-box-shadow: 0 0 0px 1000px white inset;}

input {
  /* ios下背景色会默认渐变显示，并且会有圆角 */
  -webkit-appearance: none;
  border-radius: 0;
  line-height: normal;
  /* 光标问题  */
}
select{
    border-radius: 0;
    line-height: normal;
    background: var(--cobg);
}










/* 主体 */
.centent{
    width: 100vw;
    max-width: 100%;
    /*min-width: 350px;*/
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    
    /*background: blueviolet;*/
/* 设置宽度为100vw 铺满屏幕 最大宽度100%去掉windows系统横向滚动条 高度100vh铺满屏幕 */
}



.sh-main{
    width: 100%;
    max-width: 550px;
    /*min-width: 350px;*/
    background: var(--cobg);
    z-index: 1;
}




/* 头部 */
.sh-main-head{
    width: 100%;
    height: 300px;
    position:relative;
}
.sh-main-head-top{
    width: 100%;
    max-width: 550px;
    /*min-width: 350px;*/
    height: 52px;
    /* background: olivedrab; */
    position:fixed;
    z-index: 10;
    top: 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    transition:all 0.2s;
}
.sh-main-head-img{
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position-x: 0;
    background-repeat: no-repeat;
    background-position: center;
}


.sh-main-head-top-left{
    display: flex;
}
.sh-main-head-top-left-s{
    width: 40px;
    height: 40px;
    /* background: rgb(128, 73, 0); */
    display: flex;
    justify-content: center;
    align-items: center;
    margin-left: 10px;
    cursor: pointer;
}
.sh-main-head-top-left-s>img{
    width: 50%;
    height: 50%;
}

.sh-main-head-top-right{
    display: flex;
}
.sh-main-head-top-right-s{
    width: 40px;
    height: 40px;
    /* background: rgb(128, 73, 0); */
    display: flex;
    justify-content: center;
    align-items: center;
    margin-right: 10px;
    cursor: pointer;
}

.sh-main-head-top-right-s>a{
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
}
/*.sh-main-head-top-right-s>img{
    /*width: 50%;
    height: 50%;*/
    /*width: 20px;
    height: 20px;
}*/
.sh-main-head-top-right-s>a>img{
    width: 50%;
    height: 50%;
}




/*个人主页上传背景按钮*/
.sh-main-head-top-scba{
    width: 40px;
    height: 40px;
    /* background: rgb(128, 73, 0); */
    display: flex;
    justify-content: center;
    align-items: center;
    /*margin-right: 10px;*/
    cursor: pointer;
    position: absolute;
    bottom: 0;
    left: 0;
}
.sh-main-head-top-scba>img{
    width: 60%;
    height: 60%;
}









/* 头像板块 */
.sh-main-head-headimg{
    width: 100%;
    /* height: 50px; */
    /* background: turquoise; */
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    margin-bottom: 10px;
}
.sh-main-head-headimg-tx{
    /* background: azure; */
    display: flex;
    margin-right: 25px;
    margin-top: -42px;
    z-index: 1;
}
.sh-main-head-headimg-tx>a{
    overflow: hidden;
    border-radius: 7px;
    /*-webkit-appearance: none;
    -webkit-backface-visibility: hidden;
    -webkit-transform: translate3d(0, 0, 0);*/
}
.sh-main-head-headimg-tx>a>img{
    width: 60px;
    height: 60px;
    object-fit: cover;
    border-radius: 7px;
}
.sh-main-head-headimg-tx>h4{
    margin-top: 10px;
    margin-right: 15px;
    margin-left: 10px;
    color: var(--textbs);
    font-size: 16px;
    font-weight: bold;
    max-width: 350px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.sh-main-head-headimg-qm{
    margin-right: 25px;
    z-index: 1;
}
.sh-main-head-headimg-qm>p{
    font-size: 12px;
    margin-top: 8px;
    margin-left: 20px;
    color: var(--texths);
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
    -webkit-user-select:text;
	-moz-user-focus: text;
    -moz-user-select: text;
}







/* 内容板块 */

.sh-content{
    width: 100%;
    display: flex;
    margin-bottom: 10px;
    border-bottom: 1px solid var(--fgxys);
}

/*全文按钮*/
.sh-content-quanwenan{
    width: fit-content;
    color: var(--thetitle);
    font-size: 14px;
    margin-top: 5px;
}

/* 左边 */
.sh-content-left{
    width: 46px;
    height: 46px;
    display: flex;
    margin-left: 25px;
    margin-right: 10px;
    margin-top: 5px;
    flex-shrink: 0;
    border-radius: 4px;
    overflow: hidden;
    /*-webkit-appearance: none;
    -webkit-backface-visibility: hidden;
    -webkit-transform: translate3d(0, 0, 0);*/
    
}
.sh-content-left>img{
    width: 46px;
    height: 46px;
    border-radius: 4px;
    object-fit: cover;
    cursor:pointer;
}

/* 右边 */
.sh-content-right{
    width: 100%;
    /* background: violet; */
    margin-right: 25px;
    /* border-bottom: 1px solid var(--fgxys); */
    margin-bottom: 15px;
    margin-top: 5px;
}
.sh-content-right-head{
    width: 100%;
    display: flex;
    flex-direction: column;
}
.sh-content-right-head-title{
    width: 100%;
    display: flex;
    justify-content:space-between;
    align-items: center;
    margin-bottom: 4px;
}
.sh-content-right-head-title-ad{
    width: 35px;
    height: 22px;
    display: flex;
    justify-content: center;
    align-items: center;
    background: var(--backbg);
    border-radius: 2px;
}
.sh-content-right-head-title>p{
    font-size: 15px;
    color: var(--thetitle);
    font-weight: bold;
}
.sh-content-right-head>span{
    font-size: 14px;
    color: var(--textqh);
    word-wrap:break-word;  
    word-break:break-all;  
    overflow: hidden;
    -webkit-user-select:text;
	-moz-user-focus: text;
    -moz-user-select: text;
}
.sh-content-right-head>span>code{
    /*display: inline-block;
    border-radius: 4px;
    font-size: 13px;
    background: var(--themetm);
    color: var(--theme);
    padding: 0 4px;
    vertical-align: initial;
    word-break: break-word;
    margin: 0 4px;
    font-family: inherit;*/
    display: inline-block;
    border-radius: 4px;
    font-size: 13px;
    background: var(--fgxys);
    color: var(--texths);
    padding: 0 4px;
    vertical-align: initial;
    word-break: break-word;
    margin: 0 4px;
    font-family: inherit;
}

/*文章内容超过3行自动换行*/
.wzndhycyc{
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 5;
    overflow: hidden;
}

.sh-content-right-head-title-ad>p{
    font-size: 12px;
    color: var(--adgg);
}


/* 右边图片 */
.sh-content-right-img{
    width: 85%;
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 5px;
    /* background: turquoise; */
    margin-top: 8px;
}

.sh-content-right-img-pic{
    position:relative;
    width:100%;/*相当于100px*/
    padding-top:100%;/*相当于100px.这是比较关键的一步，margin和padding是相对于其父元素的宽度的100%*/
    /*background: var(--imgbgy);*/
    
    overflow: hidden;
    border-radius: 4px;

    /*-webkit-appearance: none;
    -webkit-backface-visibility: hidden;
    -webkit-transform: translate3d(0, 0, 0);*/
}
.sh-content-right-img-pic>img{
    position:absolute;
    top:0;
    left:0;
    /*width:100%;*//*相当父元素的100%是100px*/
    /*min-width: 50%;*/
    width: fit-content;
    max-width: 100%;
    background: var(--imgbgy);
    height:100%;/*相当父元素的100%是100px*/
    object-fit: cover;
    object-position: center;
    cursor: zoom-in;
    border-radius: 4px;
}
.sh-content-right-img-pic-mask{
    font-size: 20px;
    color: var(--textbs);
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    background: rgb(0,0,0,0.5);
    pointer-events: none;
}



/* 右边图片下面定位 */
.sh-content-right-gps{
    width: 100%;
    margin-top: 5px;
}
.sh-content-right-gps>a{
    font-size: 13px;
    color: var(--thetitle);
    display: -webkit-box;
-webkit-box-orient: vertical;
-webkit-line-clamp: 1;
overflow: hidden;
}

/* 广告链接 */
.sh-content-right-ggurl{
    width: 100%;
    display: flex;
    align-items: center;
    margin-top: 10px;
}
.sh-content-right-ggurl>a{
     font-size: 13px;
     color: var(--thetitle);
     /*font-weight: bold;*/
     display: -webkit-box;
     -webkit-box-orient: vertical;
     -webkit-line-clamp: 1;
     overflow: hidden;
     max-width: 80%;
}
.sh-content-right-ggurl>img{
    width: 14px;
    height: 14px;
    margin-right: 5px;
    
}



/* 时间与点赞 */
.sh-content-right-time{
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    /* margin-top: 8px; */
    height: 38px;
}

.sh-content-right-time-left{
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.sh-content-right-time-left>span{
    font-size: 12px;
    color: var(--texths);
}
.sh-content-right-time-left>i{
    font-size: 15px;
    color: var(--thetitle);
    cursor: pointer;
    margin-left: 10px;
}
.sh-content-right-time-left-del{
    font-size: 12px;
    color: var(--thetitle);
    margin-left: 5px;
    cursor: pointer;
}
.sh-content-right-time-left-det{
    font-size: 12px;
    color: var(--thetitle);
    margin-left: 5px;
    /*margin-right: 5px;*/
    cursor: pointer;
}
.sh-content-right-time-right{
    display: flex;
    align-items: center;
    flex-shrink: 0;
    /* background: var(--thetitle); */
}


.sh-content-right-time-right-right{
    width: 30px;
    height: 20px;
    background: var(--backbg);
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 2px;
    cursor: pointer;

    -webkit-tap-highlight-color: rgba(255, 255, 255, 0);
    -webkit-user-select:none;
	-moz-user-focus: none;
    -moz-user-select: none;
}

.sh-content-right-time-right-right>p{
    width: 4px;
    height: 4px;
    border-radius: 100%;
    background: var(--thetitle);
    pointer-events: none;
}
.zp1{
    margin-right: 5px;
    pointer-events: none;
}


/* 左侧点赞 */
.sh-content-right-time-right-left{
    height: 38px;
    background: var(--bganh);
    /* display: flex; */
    display: none;
    align-items: center;
    border-radius: 4px;
    margin-right: 5px;
    position: relative;
    animation:myfirsjs 0.3s;
    -webkit-animation:myfirsjs 0.3s; /* Safari and Chrome */
}
@keyframes myfirsjs
{
    0%  {opacity:0;right:-10px;}
    50% {right:0px;}
    100% {right:0px;}
}
.sh-content-right-time-right-left>p{
    width: 1px;
    height: 50%;
    background: var(--dzplcdfg);
}
.sh-content-right-time-right-left-z{
    /*width: 100%;*/
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    /* margin-left: 20px;
    margin-right: 20px; */
    padding-left: 20px;
    padding-right: 20px;
    cursor: pointer;
}
.sh-content-right-time-right-left-y{
    /*width: 100%;*/
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    /* margin-left: 20px;
    margin-right: 20px; */
    padding-left: 20px;
    padding-right: 20px;
    cursor: pointer;
}
.sh-content-right-time-right-left-z>span{
    display: flex;
    font-size: 14px;
    color: var(--iconbs);
    margin-left: 5px;
}
.sh-content-right-time-right-left-z>img{
    width: 18px;
    height: 18px;
}
.sh-content-right-time-right-left-y>span{
    display: flex;
    font-size: 14px;
    color: var(--iconbs);
    margin-left: 5px;
    pointer-events: none;
    flex-shrink: 0;
}
.sh-content-right-time-right-left-y>img{
    width: 18px;
    height: 18px;
    pointer-events: none;
}






/* 点赞列表与评论 */
.sh-zanp{
    width: 100%;
    background: var(--fgxys);
    margin-top: 5px;
    border-radius: 4px;
}
.sh-zanp-zan{
    display: flex;
    background: var(--backbg);
    border-radius: 4px;
    /* align-items: center; */
}
.sh-zanp-zan-left{
    display: flex;
    /* align-items: center; */
    margin-left: 10px;
    margin-right: 10px;
    margin-top: 5px;
    margin-bottom: 5px;
}
.sh-zanp-zan-left>img{
    width: 18px;
    height: 18px;
}


.sh-zanp-zan-right{
    /* list-style:none; */
    display: flex;
    align-items: center;
    margin-top: 5px;
    margin-right: 10px;
    margin-bottom: 5px;
    flex-wrap: wrap;
    
    /*display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 10;
    overflow: hidden;*/
}
.sh-zanp-zan-right>li{
    display: inline-block;
    font-size: 14px;
    color: var(--thetitle);
    /*margin-right: 5px;*/
    /*flex-shrink: 0;*/
}

/*.sh-zanp-zan-right li:not(n)::after {
  content: '，';
}
.sh-zanp-zan-right li:last-child:nth-last-child(1):not(.more)::after {
  content: none;
}*/


/*.sh-zanp-zan-right li:nth-child(n+2)::before {
  content: ", ";
}

.sh-zanp-zan-rightr li:last-child::before {
  content: "";
}*/

/* 添加逗号到具有2个或以上数量的li元素 */
.sh-zanp-zan-right li:not(:last-child)::after {
  content: "，";
}

/* 可选：对于自动生成的逗号，可以添加空格以增加可读性 */
/*.sh-zanp-zan-right li:not(:last-child)::after {
  content: "， ";
}*/

/* 清除详情页的点赞头像分割符 */
.sh-zanp-zan-right2 li:not(:last-child)::after {
  content: "";
}

/* 评论列表 */
.sh-zanp-pl{
    width: 100%;
    font-size: 14px;
    /*border-top: 1px solid var(--fgxys);*/
    padding-top: 5px;
    padding-bottom: 5px;
    border-radius: 4px;
    background: var(--backbg);
    margin-top: 1px;
}
.sh-zanp-pl>li{
    display: flex;
    justify-content: space-between;
    margin-left: 10px;
    margin-right: 10px;
    line-height: 25px;
    -webkit-user-select:text;
	-moz-user-focus: text;
    -moz-user-select: text;
}
/* .sh-zanp-pl-n{
    padding-top: 5px;
    padding-bottom: 5px;
} */
.sh-zanp-pl-n{
    width: 100%;
    pointer-events:none;
    color: var(--thetitle);
}

.sh-zanp-pl-n-nc{
    color: var(--thetitle);
}
.sh-zanp-pl-n-nr{
    pointer-events: none;
    color: var(--textqh);
    word-wrap:break-word;  
    word-break:break-all;  
    overflow: hidden;
    -webkit-user-select:text;
	-moz-user-focus: text;
    -moz-user-select: text;
}
/*.sh-zanp-pl-n-nr>img{
    width: 25px;
    height: 25px;
    vertical-align: middle;
}*/
.sh-zanp-pl-del{
    height: fit-content;
    color: var(--thetitle);
    flex-shrink: 0;
    font-size: 14px;
    pointer-events: all;
    margin-left: 5px;
}




.sh-zanp-pl-ku{
    background: var(--backbg);
    border-radius: 4px;
}
.sh-zanp-pl-gd{
    width: 30px;
    height: 20px;
    background: var(--backbg);
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 2px;
    margin-left: 6px;
}
.sh-zanp-pl-gd>p{
    width: 4px;
    height: 4px;
    border-radius: 100%;
    background: var(--thetitle);
    pointer-events: none;
}
.zp1{
    margin-right: 4px;
    pointer-events: none;
}






/* 加载更多提示 */
.footer{
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 10px;
    margin-bottom: 10px;
}
.footer-text{
    font-size: 14px;
    color: var(--texths);
    margin-left: 25px;
    margin-right: 25px;
    cursor:pointer;
}








/* 评论框 */
.sh-pinglun{
    width: 95%;
    background: var(--cobg);
    /* border: 1px solid var(--theme); */
    /* box-shadow: inset 0px 0px 0px 1px var(--theme); */
    border-radius: 4px;
    /* margin-top: 5px;
    margin-bottom: 5px; */
    box-sizing: border-box;
    transition: all 0.25s;
    /* margin-bottom: 10px; */
    /*padding: 0 0 8px 0;*/
}
.sh-pinglun-s{
    /* margin: 8px; */
    /*display: flex;
    align-items: flex-end;*/
    display: flex;
    margin: 8px 8px 0px 8px;
}
.sh-pinglun-s>textarea{
    width: 100%;
    height: 40px;
    outline: none;
    resize: none;
    border: 0px solid #ced4da;
    font-family: inherit;
    font-size: 14px;
    background: var(--cobg); 
    /*margin-top: 8px;*/
    max-height: 150px;
    min-height: 40px;
    color: var(--textqh);
    overflow-x: hidden;
    overflow-y: scroll;
    overscroll-behavior-y: contain;
    display: flex;
}

/*设置滚动条宽度*/
.sh-pinglun-s>textarea::-webkit-scrollbar {
        width: 4px;
    }
/*设置外层滑动槽的颜色*/
.sh-pinglun-s>textarea::-webkit-scrollbar-track {
    background-color: rgb(0,0,0,0);
}
/*设置滑动条的颜色*/
.sh-pinglun-s>textarea::-webkit-scrollbar-thumb {
    background-color: var(--gdwys);
    border-radius: 4px;
}



.sh-pinglun-biao{
    /* display: grid; */
    max-height: 95px;
    display: none;
    justify-content: space-between;
    /*grid-template-columns: repeat(auto-fill, 1.625em);*/
    grid-template-columns: repeat(auto-fill, 2em);
    row-gap: 0.625em;
    column-gap: 0.180em;
    justify-items: center;
    margin-left: 10px;
    margin-right: 10px;
    margin-top: 10px;
    overflow-x: hidden;
    overflow-y: scroll;
    overscroll-behavior-y: contain;
    /*height: 95px;*/
    -webkit-animation: move_1 0.25s;
}
.sh-pinglun-biao>img{
    width: 25px;
    height: 25px;
    cursor: pointer;
    transition: all 0.25s;
}
.sh-pinglun-biao>img:hover{
    transform: scale(1.1);
}

/*设置滚动条宽度*/
.sh-pinglun-biao::-webkit-scrollbar {
        width: 4px;
    }
/*设置外层滑动槽的颜色*/
.sh-pinglun-biao::-webkit-scrollbar-track {
    background-color: rgb(0,0,0,0);
}
/*设置滑动条的颜色*/
.sh-pinglun-biao::-webkit-scrollbar-thumb {
    background-color: var(--gdwys);
    border-radius: 4px;
}






.sh-pinglun-fs{
    width: 100%;
    display: flex;
    justify-content: flex-end;
    margin-top: 8px;
    margin-bottom: 8px;
}
.sh-pinglun-fs-right{
    display: flex;
    align-items: center;
    /*margin-bottom: 10px;*/
}
.sh-pinglun-fs-right-bqimg{
    display: flex;
    align-items: center;
    margin-right: 10px;
}

.sh-pinglun-fs-right-fs{
    width: 70px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    background: var(--backbg);
    color: var(--thetitle);
    margin-right: 10px;
    cursor: pointer;
    transition: all 0.25s;
}
.sh-pinglun-fs-right-fs>span{
    font-size: 14px;
}






/*.sh-plkz{
    width: 100%;
    max-width: 550px;
    height: 100%;
    background: #00000026;
    position:fixed; bottom:0;
    z-index: 11;
    align-items: flex-end;
    display: none;
}*/

.sh-pinglunkuang{
    width: 100%;
    /*height: 50%;*/
    /* min-height: 30%; */
    background: var(--cobg);
    /*position: relative;*/
    position:sticky;
    /*animation:myfirsjss 0.5s;
    /*-webkit-animation:myfirsjss 0.5s; /* Safari and Chrome */
    border-top-right-radius: 4px;
    border-top-left-radius: 4px;
    /* padding: 10px 10px 10px 10px; */
    background: var(--backbg);
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 5px 0;
}













/*参数 */
.huifucanshu{
    /* visibility: hidden; */
    display: none;
    color: #ffffff00;
}










/* 登录与注册 */
.sh-login{
    width: 100%;
    max-width: 550px;
    height: 100%;
    display: none;
    justify-content: center;
    align-items: center;
    background: #00000026;
    position: fixed;
    bottom: 0;
    z-index: 11;
    /*-webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);*/
    -webkit-animation: move_2 0.20s;
    backdrop-filter: saturate(100%) blur(2px);
    -webkit-backdrop-filter: saturate(100%) blur(2px);
}

.sh-login-main{
    width: 80%;
    /* width: 100%; */
    /* height: 100%; */
    background: var(--cobg);
    /*background: rgb(255,255,255,0.8);*/
    border-radius: 4px;
    /* box-shadow: 0 0 10px rgb(0 0 0 / 10%);*/
    backdrop-filter: saturate(180%) blur(20px);
    -webkit-backdrop-filter: saturate(180%) blur(20px);
    -webkit-animation: move_1 0.20s;
}

.sh-login-main-top{
    width: 100%;
    /*height: 35px;*/
    display: flex;
    justify-content: flex-end;
    align-items: center;
}
.sh-login-main-top-img{
    width: 30px;
    height: 30px;
    margin-right: 8px;
    margin-top: 8px;
    cursor:pointer;
}
.sh-login-main-kko{
    width: 100%;
    height: 90%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}
.sh-login-main-kko-kok-p{
    margin-left: 30px;
    /*margin-top: 30px;*/
    margin-bottom: 20px;
    font-size: 20px;
    position: relative;
    color: var(--textqh);
}
.sh-login-main-kko-kok-p:after {
  content: "";
  width:60px;
  height:3px;
  background:var(--theme);
  position:absolute;
  bottom:-5px;
  left:0;
  border-radius: 4px;
  box-shadow: 1px 1px 3px -1px var(--theme);
  transition:all 0.20s;
}
.sh-login-main-kko-kok-p:hover:after {
  width:80px;
}

.sh-login-main-con{
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    margin-top: 10px;
}
.sh-login-main-con-anu{
    width: 100%;
    display: flex;
    align-items: center;
    margin-bottom: 5px;
}
.sh-login-main-con-anu>p{
    margin-left: 30px;
    margin-right: 20px;
    font-size: 15px;
    flex-shrink: 0;
    color: var(--textqh);
}
.sh-login-main-con-anu>a{
    font-size: 15px;
    color: var(--thetitle);
    margin-left: 30px;
    margin-top: 15px;
}
.sh-login-main-con-anu>input{
    width: 100%;
    height: 40px;
    border: none;
    outline: none;
    font-size: 12px;
    margin-right: 30px;
    border-bottom: 1px solid var(--fgxys);
    background: rgb(255,255,255,0);
    color: var(--textqh);
}
.sh-login-main-bot{
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 30px;
}
.sh-left{
    width: 50%;
    height: 40px;
    border: none;
    outline: none;
    border-radius: 4px;
    color: var(--thetitle);
    background: var(--backbg);
    cursor: pointer;
    margin-bottom: 10px;
}
.sh-right{
    width: 50%;
    height: 40px;
    border: none;
    outline: none;
    border-radius: 4px;
    color: #ffffff;
    background: var(--theme);
    cursor: pointer;
}


.sh-login-main-bottom{
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 20px 0;
}
.sh-login-main-bottom>a{
    font-size: 12px;
    color: var(--thetitle);
}
.sh-login-main-bottom>p{
    width: 1px;
    height: 15px;
    background: var(--thetitle);
    margin-left: 5px;
    margin-right: 5px;
}







/* 发布选择框 */
.sh-fabu{
    width: 100%;
    max-width: 550px;
    height: 100%;
    display: none;
    justify-content: center;
    align-items: flex-end;
    background: #00000026;
    position: fixed;
    bottom: 0;
    z-index: 11;/*11*/
    -webkit-animation: move_2 0.20s; /* Chrome, Safari */  
    -moz-animation: move_2 0.20s; /* Firefox */  
    -o-animation: move_2 0.20s; /* Opera */  
    -ms-animation: move_2 0.20s; /* IE 10+ */  
    animation: move_2 0.20s; /* Standard syntax */
    /*-webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);*/
    /*backdrop-filter: saturate(100%) blur(2px);
    -webkit-backdrop-filter: saturate(100%) blur(2px);*/
}
.sh-fabu-main{
    width: 100%;
    background: var(--cobg);
    border-radius: 4px 4px 0 0;
    box-shadow: 0 0 10px rgb(0 0 0 / 10%);
    /* transition: all 0.20s; */
    -webkit-animation: move_1 0.20s;
    -moz-animation: move_1 0.20s;
    -o-animation: move_1 0.20s;
    -ms-animation: move_1 0.20s;
    animation: move_1 0.20s;
}
.sh-fabu-main-top {
    width: 100%;
    /* height: 35px; */
    display: flex;
    justify-content: flex-end;
    align-items: center;
}
.sh-fabu-main-top-xiaoxih {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
.sh-fabu-main-top-xiaoxih>span {
    font-size: 15px;
    margin-left: 15px;
    margin-top: 8px;
    color: var(--textqh);
}
.sh-fabu-main-top-div {
    width: 35px;
    height: 35px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-right: 8px;
    margin-top: 8px;
    cursor: pointer;
}
.sh-fabu-con {
    width: 100%;
    max-height: 500px;
    /*display: flex;
    flex-direction: column;*/
    /*min-height: 100px;*/
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    /*gap: 10px;*/
    row-gap:15px;
    margin-top: 6px;
    overflow-x: hidden;
    overflow-y: scroll;
    overscroll-behavior-y: contain;
    margin-bottom: 20px;
}

.sh-fabu-con-k{
    display: flex;
    justify-content: center;
    align-items: center;
    /*height: 65px;*/
    /*background: var(--backbg);
    border-radius: 4px;
    padding: 10px 0;*/
}
.sh-fabu-con-kk{
    width: 90%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background: var(--backbg);
    border-radius: 4px;
    padding: 10px 0;
    cursor: pointer;
}
.sh-fabu-con-kk>span{
    font-size: 14px;
    color: var(--textqh);
    margin-top: 5px;
    pointer-events: none;
}
.sh-fabu-con-kk>i{
    font-size: 25px;
    pointer-events: none;
}


/*设置滚动条宽度*/
.sh-fabu-con::-webkit-scrollbar {
        width: 4px;
    }
/*设置外层滑动槽的颜色*/
.sh-fabu-con::-webkit-scrollbar-track {
    background-color: rgb(0,0,0,0);
}
/*设置滑动条的颜色*/
.sh-fabu-con::-webkit-scrollbar-thumb {
    background-color: var(--gdwys);
    border-radius: 4px;
}







/* 消息通知弹窗 */
.sh-news{
    width: 100%;
    max-width: 550px;
    height: 100%;
    display: none;
    justify-content: center;
    align-items: center;
    background: #00000026;
    position: fixed;
    bottom: 0;
    z-index: 11;/*11*/
    -webkit-animation: move_2 0.20s; /* Chrome, Safari */  
    -moz-animation: move_2 0.20s; /* Firefox */  
    -o-animation: move_2 0.20s; /* Opera */  
    -ms-animation: move_2 0.20s; /* IE 10+ */  
    animation: move_2 0.20s; /* Standard syntax */
    /*-webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);*/
    backdrop-filter: saturate(100%) blur(2px);
    -webkit-backdrop-filter: saturate(100%) blur(2px);
}
.sh-news-main{
    width: 80%;
    background: var(--cobg);
    border-radius: 4px;
    box-shadow: 0 0 10px rgb(0 0 0 / 10%);
    /*transition: all 0.20s;*/
    -webkit-animation: move_1 0.20s; /* Chrome, Safari */  
    -moz-animation: move_1 0.20s; /* Firefox */  
    -o-animation: move_1 0.20s; /* Opera */  
    -ms-animation: move_1 0.20s; /* IE 10+ */  
    animation: move_1 0.20s; /* Standard syntax */
    /* position:relative; */
}

.sh-news-main-top{
    width: 100%;
    /*height: 35px;*/
    display: flex;
    justify-content: flex-end;
    align-items: center;
    /* position: absolute;
    top: 0px; */
    /* background: var(--cobg);
    z-index: 1; */
}
.sh-news-main-top-xiaoxih{
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
.sh-news-main-top-xiaoxih>span{
    font-size: 15px;
    margin-left: 15px;
    margin-top: 8px;
    color: var(--textqh);
}
.sh-news-main-top-div{
    width: 35px;
    height: 35px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-right: 8px;
    margin-top: 8px;
    cursor: pointer;
}
.sh-news-main-top-div2{
    position: relative;
}
.sh-news-main-top-div-menu {
    width: 160px;
    background: var(--cobg);
    box-shadow: 0 0 10px rgb(0 0 0 / 10%);
    border-radius: 4px;
    display: none;
    flex-direction: column;
    position: absolute;
    top: 40px;
    right: 0px;
    z-index: 5;
}
.sh-news-main-top-div-menu>a {
    width: 100%;
    height: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 14px;
    transition: all 0.2s;
    -webkit-tap-highlight-color: rgba(255, 255, 255, 0);
    -webkit-user-select: none;
    -moz-user-focus: none;
    -moz-user-select: none;
    text-decoration: none;
}
.sh-news-main-top-div-menu>a:hover {
    color: var(--theme);
}
.sh-news-main-top-div-menu>a::before {
    margin-right: 5px;
}


.sh-news-con{
    width: 100%;
    max-height: 500px;
    display: flex;
    flex-direction: column;
    /*overflow: scroll;*/
    margin-top: 6px;
    /*-ms-overflow-style: none;  /* Internet Explorer 10+ */
    /*scrollbar-width: none;     /* Firefox */
    /*scrollbar-face-color:var(--texths);*/
    
    overflow-x: hidden;
    overflow-y: scroll;
    overscroll-behavior-y: contain;
}

/*设置滚动条宽度*/
.sh-news-con::-webkit-scrollbar {
        width: 4px;
    }
/*设置外层滑动槽的颜色*/
.sh-news-con::-webkit-scrollbar-track {
    background-color: rgb(0,0,0,0);
}
/*设置滑动条的颜色*/
.sh-news-con::-webkit-scrollbar-thumb {
    background-color: var(--gdwys);
    border-radius: 4px;
}

.sh-news-con-lie{
    width: 100%;
    height: 70px;
    min-height: 70px;
    display: flex;
    align-items: center;
    cursor: pointer;
    /* margin-top: 10px;
    margin-bottom: 10px; */
}
.sh-news-con-lie-left{
    display: flex;
    /* background: var(--thetitle); */
    /* background: var(--theme); */
    position:relative;
    margin-left: 15px;
    pointer-events: none;
    /* display: flex;
    justify-content: center;
    align-items: center; */
    
}
.sh-news-con-lie-left-imgt{
    display: flex;
    overflow: hidden;
    border-radius: 4px;
    /*-webkit-appearance: none;
    -webkit-backface-visibility: hidden;
    -webkit-transform: translate3d(0, 0, 0);*/
}
.sh-news-con-lie-left>p{
    width: 10px;
    height: 10px;
    display: flex;
    justify-content: center;
    align-items: center;
    background: red;
    border-radius: 100%;
    font-size: 12px;
    color: var(--cobg);
    position: absolute;
    margin-left: 38px;
    margin-top: -4px;
    z-index: 1;
}
.sh-news-con-lie-left-img{
    width: 45px;
    height: 45px;
    object-fit: cover;
    border-radius: 4px;
}

.sh-news-con-lie-right{
    width: 100%;
    height: 50px;
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    pointer-events: none;
    flex: 1;
}
.sh-news-con-lie-right-title{
    font-size: 14px;
    margin-left: 15px;
    margin-right: 15px;
    display: -webkit-box;
-webkit-box-orient: vertical;
-webkit-line-clamp: 1;
overflow: hidden;
color: var(--thetitle);
}
.sh-news-con-lie-right-time{
    color: var(--texths);
    font-size: 12px;
    margin-left: 10px;
}
.sh-news-con-lie-right-text{
    font-size: 12px;
    margin-left: 15px;
    margin-right: 15px;
    display: -webkit-box;
-webkit-box-orient: vertical;
-webkit-line-clamp: 1;
overflow: hidden;
color: var(--textqh);
}


/* 消息弹窗底部提示 */
.sh-news-tishi{
    width: 100%;
    height: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
}
.sh-news-tishi>p{
    font-size: 12px;
    color: var(--texths);
    margin: 0 15px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}








/* 友链弹窗 */
.sh-link{
    width: 100%;
    max-width: 550px;
    height: 100%;
    display: none;
    justify-content: center;
    align-items: center;
    background: #00000026;
    position: fixed;
    bottom: 0;
    z-index: 11;
    -webkit-animation: move_2 0.20s; /* Chrome, Safari */  
    -moz-animation: move_2 0.20s; /* Firefox */  
    -o-animation: move_2 0.20s; /* Opera */  
    -ms-animation: move_2 0.20s; /* IE 10+ */  
    animation: move_2 0.20s; /* Standard syntax */
    /*-webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);*/
    backdrop-filter: saturate(100%) blur(2px);
    -webkit-backdrop-filter: saturate(100%) blur(2px);
}
.sh-link-main{
    width: 80%;
    background: var(--cobg);
    border-radius: 4px;
    box-shadow: 0 0 10px rgb(0 0 0 / 10%);
    /*transition: all 0.20s;*/
    -webkit-animation: move_1 0.20s; /* Chrome, Safari */  
    -moz-animation: move_1 0.20s; /* Firefox */  
    -o-animation: move_1 0.20s; /* Opera */  
    -ms-animation: move_1 0.20s; /* IE 10+ */  
    animation: move_1 0.20s; /* Standard syntax */
}
.sh-link-main-top{
    width: 100%;
    /*height: 35px;*/
    display: flex;
    justify-content: flex-end;
    align-items: center;
    /* position: absolute;
    top: 0px; */
    /* background: var(--cobg);
    z-index: 1; */
}
.sh-link-main-top-img{
    width: 30px;
    height: 30px;
    margin-right: 8px;
    margin-top: 8px;
    cursor:pointer;
}
.sh-link-con{
    width: 100%;
    max-height: 500px;
    display: flex;
    flex-direction: column;
    /*overflow: scroll;
    -ms-overflow-style: none;  /* Internet Explorer 10+ */
    /*scrollbar-width: none;     /* Firefox */
    margin-top: 6px;
    
    overflow-x: hidden;
    overflow-y: scroll;
    overscroll-behavior-y: contain;
}

/*设置滚动条宽度*/
.sh-link-con::-webkit-scrollbar {
        width: 4px;
    }
/*设置外层滑动槽的颜色*/
.sh-link-con::-webkit-scrollbar-track {
    background-color: rgb(0,0,0,0);
}
/*设置滑动条的颜色*/
.sh-link-con::-webkit-scrollbar-thumb {
    background-color: var(--gdwys);
    border-radius: 4px;
}


.sh-link-con-lie{
    width: 100%;
    height: 55px;
    min-height: 55px;
    display: flex;
    align-items: center;
    /* margin-top: 10px;
    margin-bottom: 10px; */
}
.sh-link-con-lie-left{
    display: flex;
    /* background: var(--thetitle); */
    /* background: var(--theme); */
    position:relative;
    margin-left: 15px;
    /* display: flex;
    justify-content: center;
    align-items: center; */
    overflow: hidden;
    border-radius: 4px;
    flex-shrink: 0;
    /*-webkit-appearance: none;
    -webkit-backface-visibility: hidden;
    -webkit-transform: translate3d(0, 0, 0);*/
}
.sh-link-con-lie-left-img{
    width: 40px;
    height: 40px;
    object-fit: cover;
    border-radius: 4px;
}

.sh-link-con-lie-right-title{
    font-size: 14px;
    margin-left: 15px;
    margin-right: 15px;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    overflow: hidden;
    color: var(--textqh);
}
/* 消息弹窗底部提示 */
.sh-link-tishi{
    width: 100%;
    height: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
}
.sh-link-tishi>p{
    font-size: 12px;
    color: var(--texths);
    margin: 0 15px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}





/* 帖子内容 视频 */
.sh-video{
    /* width: calc(150% * 1 / 2); */
    /* width: calc(125% * 2 / 3); */
    /*width: 85%;*//*-原属性*/
    display: flex;
    justify-items: center;
    align-items: center;
    position:relative;
    margin-top: 8px;
    /*增加属性↓*/
    width: fit-content;
    background: rgb(62 62 62);
    max-width: 85%;
    border-radius: 4px;
    /*position: relative;
    width: 100%;
    padding-top: 50%;
    margin-top: 8px;*/
}
.sh-video>i{
    width: fit-content;
    height: fit-content;
    grid-column: 1;
    grid-row: 1;
    z-index: 10;
    position: absolute;
    top: 0px;
    right: 0px;
    font-size: 20px;
    /*color: rgb(255,255,255,0.50);
    pointer-events: none;*/
    color: var(--bganh);
    display: none;
    cursor: pointer;
    padding: 15px;
    
}


.sh-video-span{
    width: fit-content;
    height: fit-content;
    grid-column: 1;
    grid-row: 1;
    z-index: 1;
    position: absolute;
    bottom: 8px;
    left: 8px;
    font-size: 12px;
    color: rgb(255 255 255);
    pointer-events: none;
    background: rgb(0,0,0,0.28);
    backdrop-filter: saturate(180%) blur(20px);
    -webkit-backdrop-filter: saturate(180%) blur(20px);
    padding: 1px 3px;
    border-radius: 4px;
}
.sh-video>iframe{
    /*max-width: 100%;
    max-height: 280px;*/
    width: 100vw;
    max-width: 100%;
    max-height: 280px;
    min-height: 200px;
    border-radius: 4px;
    /*margin-top: 8px;*//*-原属性*/
    /*增加属性↓*/
    /*position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: none;*/
}
.sh-content-video{
    /* width: 100%; */
    /* max-height: 240px; */
    /* margin-top: 8px; */
    max-width: 100%;
    max-height: 280px;
    /*margin-top: 8px;*/
    cursor: pointer;
    /*background: rgb(0 0 0);*/
    /*background: var(--imgbgy);*/
    /*background: var(--backbg);*/
    border-radius: 4px;
    grid-column: 1;
    grid-row: 1;
}
.sh-homecontent-wzsbs{
    position: absolute;
    bottom: 5px;
    right: 5px;
    color: var(--iconbs);
    /*padding: 5px;*/
}

/* 隐藏视频所有按钮 */
/* video::-webkit-media-controls{
    display:none !important;
} */





/* 文章内容中的表情 */
.sh-nr-bq-img-wk{
    display: inline-table;
    overflow: hidden;
    width: fit-content;
    border-radius: 4px;
    /*-webkit-appearance: none;
    -webkit-backface-visibility: hidden;
    -webkit-transform: translate3d(0, 0, 0);*/
}
.sh-nr-bq-img{
    width: 20px;
    height: 20px;
    margin-left: 2px;
    margin-right: 2px;
    vertical-align: middle;
}



















/*首页消息小红点*/
.xiaoxhd{
    width: 6px;
    height: 6px;
    display: flex;
    justify-content: center;
    align-items: center;
    background: red;
    border-radius: 100%;
    font-size: 12px;
    color: var(--cobg);
    position: absolute;
    margin-left: 10px;
    margin-top: -18px;
}








/*home个人中心页面 删除和隐藏文章 按钮样式*/
.sh-delewz{
    width: 100%;
    display: flex;
    justify-content: flex-end;
    /*justify-content: space-between;*/
    margin-top: 10px;
}

.sh-delewz>img{
    width: 20px;
    height: 20px;
    margin-right: 5px;
    cursor:pointer;
}
.sh-delewz>img{
    width: 20px;
    height: 20px;
    margin-right: 5px;
    cursor:pointer;
}


.sh-delewz-fukuai{
    display: flex;
    align-content: center;
}
.sh-delewz-fukuai>a{
    margin-right: 10px;
}
.sh-delewz-fukuai>a>img{
    width: 20px;
    height: 20px;
    cursor:pointer;
}
.sh-delewz-fukuai>img{
    width: 20px;
    height: 20px;
    cursor:pointer;
    margin-right: 5px;
}









/*设置页面UI样式*/
.setup-main{
    background: var(--backbg);
}

.setup-main-top{
    /*background: var(--cobg);*/
    /*background:rgba(240, 240, 240, 0.8);*/
    background: var(--dbztlys);
    backdrop-filter: saturate(180%) blur(20px);
    -webkit-backdrop-filter: saturate(180%) blur(20px);
}

.setup-main-head{
    height: 52px;
}


.setup-main-head-top-left-s{
    margin-left: 0px;
    pointer-events: none;
}
.setup-main-title{
    /*color: #505050;*/
    color: var(--texths);
}

.setup-main-lieb{
    width: 100%;
    height: 45px;
    overflow: hidden;
    background: var(--cobg);
    margin-bottom: 10px;
}


.setup-main-lieb-title{
    width: 100%;
    height: 45px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: var(--cobg);
    cursor:pointer;
}
/*.setup-main-lieb-title:hover{
    background: #f1f1f1;
}*/

.setup-main-lieb-title>span{
    font-size: 15px;
    margin-left: 15px;
    display: flex;
    align-items: center;
    color: var(--textqh);
}


















/*新版资料ui*/
.sh-setup-formup{
    margin-left: 15px;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;
    background-color: var(--backbg);
    border-radius: 2px;
    box-sizing: border-box;
    width: 100px;
    height: 100px;
    /* line-height: 100px; */
    /*border-color: rgb(192, 204, 218);*/
    margin-bottom: 10px;
}
.sh-setup-formup>span{
    position: absolute;
    /*left: 38px;*/
    font-size: 28px;
    color: rgb(140, 147, 157);
}
.sh-setup-formup>img{
    width: 100px;
    height: 100px;
    display: none;
    object-fit: cover;
    object-position: center;
}
.setup-main-tj{
    width: 48px;
    height: 32px;
    display: flex;
    justify-content: center;
    align-items: center;
    background: var(--theme);
    border-radius: 4px;
    margin-bottom: 15px;
    margin-right: 15px;
    /* margin-left: 15px; */
    /* margin-bottom: 15px; */
    cursor: pointer;
    border: none;
    outline: none;
    color: #ffffff;
    font-size: 14px;
}
.sh-setup-main-xbb{
    width: 100%;
    height: 60px;
    overflow: hidden;
    margin-bottom: 1px;
    background: var(--cobg);
}
.sh-setup-main-xb{
    width: 100%;
    height: 45px;
    overflow: hidden;
    margin-bottom: 1px;
    background: var(--cobg);
}
.sh-setup-main-xbc{
    width: 100%;
    height: 45px;
    overflow: hidden;
    margin-bottom: 1px;
    background: var(--cobg);
}
.setup-main-lieb-xtitle {
    width: 100%;
    height: 60px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: var(--cobg);
    cursor: pointer;
}
.setup-main-lieb-xtitlee{
    width: 100%;
    height: 45px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: var(--cobg);
    cursor: pointer;
}
.setup-main-lieb-title-y{
    display: flex;
    align-items: center;
    margin-right: 15px;
}
.setup-main-lieb-title-y-imgt{
    width: 48px;
    height: 48px;
    object-fit: cover;
    object-position: center;
    border-radius: 4px;
    margin-right: 5px;
}
.setup-main-lieb-title-y>p{
    max-width: 150px;
    font-size: 14px;
    margin-right: 5px;
    color: #898989;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
.setup-main-lieb-xtitle>span {
    font-size: 15px;
    margin-left: 15px;
    display: flex;
    align-items: center;
    color: var(--textqh);
}

.setup-main-lieb-xtitlee>span {
    font-size: 15px;
    margin-left: 15px;
    display: flex;
    align-items: center;
    color: var(--textqh);
}



.setup-main-lieb-xcontent{
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 5px;
}
.setup-main-lieb-xcontent>input {
    margin: 0px 0px 0px 15px;
    flex: 0.9;
    height: 28px;
    -webkit-border-radius: 2px;
    border-radius: 2px;
    /*padding: 5px 12px;*/
    border: none;
    outline: none;
    background: var(--cobg);
    color: var(--textqh);
    margin-bottom: 15px;
    /*border: 1px #e7e7e7 solid;*/
}
.setup-main-lieb-xgxan{
    width: 48px;
    height: 32px;
    display: flex;
    justify-content: center;
    align-items: center;
    background: var(--theme);
    border-radius: 4px;
    margin-bottom: 15px;
    margin-right: 15px;
    /* margin-left: 15px; */
    /* margin-bottom: 15px; */
    cursor: pointer;
}
.setup-main-lieb-xgxan>span {
    color: #ffffff;
    font-size: 14px;
    pointer-events: none;
}















.setup-main-lieb-content{
    width: 100%;
    display: flex;
    flex-direction: column;
    margin-bottom: 15px;
    margin-top: 5px;
}
.setup-main-lieb-content>span{
    margin: 0px 15px 5px 15px;
    font-size: 15px;
    color: var(--thetitle);/*838383*/
    /*border-left: 2px solid var(--theme);
    background: var(--themetm);
    padding: 0px 5px;*/
    border-radius: 2px;
}
.setup-main-lieb-content>input{
    margin: 0px 15px 5px 15px;
    height: 28px;
    -webkit-border-radius: 2px;
    border-radius: 2px;
    padding: 5px 12px;
    border:none;
    outline:none;
    border: 1px var(--backbg) solid;
    background: var(--backbg);
    color: var(--textqh);
}
.setup-main-lieb-content>p{
    margin-left: 15px;
    margin-right: 15px;
    font-size: 13px;
    color: var(--protext);
    background: var(--protextbg);
    width: fit-content;
    padding: 2px 4px;
    border-radius: 4px;
}
.setup-main-lieb-content>textarea{
    padding: 5px 12px;
    margin: 0px 15px 5px 15px;
    border: 1px #e7e7e7 solid;
    outline: none;
    font-family: inherit;
    font-size: 14px;
    resize: none;
    border-radius: 2px;
    
        /* overflow: scroll;
    overflow-x: hidden; */
    overflow: scroll;
    -ms-overflow-style: none;  /* Internet Explorer 10+ */
    scrollbar-width: none;     /* Firefox */
}
.setup-main-lieb-content>textarea::-webkit-scrollbar { 
    display: none;            /* Safari and Chrome */
}




/*开关样式*/
.setup-main-lieb-content-kg{
    /*width: 100%;
    display: flex;
    flex-direction: column;
    margin-bottom: 15px;*/
    width: 100%;
    display: flex;
    /* flex-direction: column; */
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}
.setup-main-lieb-content-kg>span{
    margin-left: 15px;
    font-size: 15px;
    color: #838383;
}
.setup-main-lieb-content-kg-an{
    /*width: 100%;*/
    height: 45px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    /*background: var(--theme);*/
    margin-right: 15px;
}
.setup-main-lieb-content-kg-an-xin{
    /*width: 50px;
    height: 30px;*/
    width: 35px;
    height: 20px;
    display: flex;
    justify-content: flex-start;/*关闭时为flex-start 开启时为flex-end*/
    align-items: center;
    border-radius: 360px;
    background: rgb(238 238 238);/*开启时的背景为background: var(--theme)  关闭时的背景为background: rgb(238 238 238);*/
    -webkit-transition:all 0.5s;
    transition:all 0.5s;  /*设置动画 所有属性 执行时间为0.5s*/
    cursor: pointer;
}
.setup-main-lieb-content-kg-an-xin>p{
    width: 18px;
    height: 18px;
    background: var(--cobg);
    border-radius: 360px;
    /* border: 1px solid #03A9F4; */
    box-shadow: 0px 0px 2px #c0c0c0;
    margin-left: 1px;
    margin-right: 1px;
    -webkit-transition:all 0.5s;
    transition:all 0.5s;  /*设置动画 所有属性 执行时间为0.5s*/
}


/*退出登录*/
.setup-main-lieb-logout{
    width: 100%;
    height: 45px;
    background: var(--cobg);
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
}
.setup-main-lieb-logout>p{
    font-size: 15px;
    pointer-events: none;
    color: var(--textqh);
}




.setup-main-lieb-gxan{
    width: 56px;
    height: 38px;
    display: flex;
    justify-content: center;
    align-items: center;
    background: var(--theme);
    border-radius: 4px;
    margin-left: 15px;
    margin-bottom: 15px;
    cursor: pointer;
}
.setup-main-lieb-gxan>span{
    color: #ffffff;
    font-size: 15px;
    pointer-events: none;
}





/*搜索*/
.so{
    width: 100%;
    max-width: 550px;
    height: 100%;
    display: none;
    justify-content: center;
    background: #00000026;
    position: fixed;
    bottom: 0;
    z-index: 11;
    -webkit-animation: move_2 0.20s;
    backdrop-filter: saturate(100%) blur(2px);
    -webkit-backdrop-filter: saturate(100%) blur(2px);
}
.sobd{
    width: 90%;
    height: fit-content;
    margin-top: 50px;
    display: flex;
    -webkit-animation: move_1 0.20s;
}
.sobd-in{
    width: 90%;
    height: 24px;
    border-radius: 2px;
    padding: 5px 5px;
    outline: none;
    border: 1px var(--cobg) solid;
    background: var(--cobg);
    color: var(--textqh);
    font-size: 14px;
}
.sobd-bu{
    width: 72px;
    height: 36px;
    display: flex;
    justify-content: center;
    align-items: center;
    background: var(--cobg);
    border-radius: 2px;
    margin-left: 5px;
    cursor: pointer;
    border: 0px solid var(--cobg);
    font-size: 14px;
    outline: none;
    color: var(--textqh);
    padding: 0 5px;
    
    display: -webkit-box;
    -webkit-box-orient: vertical;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}









/*视频放大容器*/
.videofdb{
    width: 100%;
    height: 100%;
    max-width: 100%;
    display:none;
    justify-content: center;
    align-items: center;
    position: fixed;
    bottom: 0;
    left: 0;
    background: #000000;
    z-index: 11;
    border-radius: 0px;
    
    /*background: rgb(0 0 0 / 80%);
    backdrop-filter: saturate(180%) blur(20px);
    -webkit-backdrop-filter: saturate(180%) blur(20px);*/
}
.videofdb>video{
    width: auto;
    height: auto;
    max-width: 100%;
    max-height: 100%;
    object-fit: cover;
    cursor: pointer;
    background: #000000;
    border-radius: 0px;
    
    /*width: auto;
    height: auto;
    max-width: 90%;
    max-height: 80%;
    object-fit: cover;
    cursor:pointer;*/
    /*background: #000000;*/
    /*background: rgb(0 0 0 / 20%);
    border-radius: 8px;*/
}








/*首页顶部音乐*/
.sh-main-head-top-left-mu{
    width: 120px;
    height: 40px;
    display: flex;
    align-items: center;
    margin-left: 10px;
    
}

.sh-main-top-mu{
    width: 20px;
    height: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-left: 5px;
    margin-right: 5px;
    cursor: pointer;
}
.sh-main-top-g-container{
    width: 100%;
    height: 2px;
    background: rgb(215 215 215 / 75%);
    margin-left: 2px;
    margin-right: 2px;
    border-radius: 4px;
}
/*顶部旧音乐进度条样式*/
/*.sh-main-top-mucisjd{
    width: 0%;
    height: inherit;*/
    /* border-radius: 2px; */
    /*background: var(--cobg);
}*/
.sh-main-top-mu-bgmq{
    width: 20px;
    height: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-left: 5px;
    margin-right: 5px;
    cursor: pointer;
}





/*顶部新音乐进度条样式2*/
/* Shaft 2 */
[class^="shaft-load"] .shaft1 {
  -webkit-animation-delay: 0.05s;
  -moz-animation-delay: 0.05s;
  -o-animation-delay: 0.05s;
  animation-delay: 0.05s;
}
[class^="shaft-load"] .shaft2 {
  -webkit-animation-delay: 0.1s;
  -moz-animation-delay: 0.1s;
  -o-animation-delay: 0.1s;
  animation-delay: 0.1s;
}
[class^="shaft-load"] .shaft3 {
  -webkit-animation-delay: 0.15s;
  -moz-animation-delay: 0.15s;
  -o-animation-delay: 0.15s;
  animation-delay: 0.15s;
}
[class^="shaft-load"] .shaft4 {
  -webkit-animation-delay: 0.2s;
  -moz-animation-delay: 0.2s;
  -o-animation-delay: 0.2s;
  animation-delay: 0.2s;
}
[class^="shaft-load"] .shaft5 {
  -webkit-animation-delay: 0.25s;
  -moz-animation-delay: 0.25s;
  -o-animation-delay: 0.25s;
  animation-delay: 0.25s;
}
[class^="shaft-load"] .shaft6 {
  -webkit-animation-delay: 0.3s;
  -moz-animation-delay: 0.3s;
  -o-animation-delay: 0.3s;
  animation-delay: 0.3s;
}
[class^="shaft-load"] .shaft7 {
  -webkit-animation-delay: 0.35s;
  -moz-animation-delay: 0.35s;
  -o-animation-delay: 0.35s;
  animation-delay: 0.35s;
}
[class^="shaft-load"] .shaft8 {
  -webkit-animation-delay: 0.4s;
  -moz-animation-delay: 0.4s;
  -o-animation-delay: 0.4s;
  animation-delay: 0.4s;
}
[class^="shaft-load"] .shaft9 {
  -webkit-animation-delay: 0.45s;
  -moz-animation-delay: 0.45s;
  -o-animation-delay: 0.45s;
  animation-delay: 0.45s;
}
[class^="shaft-load"] .shaft10 {
  -webkit-animation-delay: 0.5s;
  -moz-animation-delay: 0.5s;
  -o-animation-delay: 0.5s;
  animation-delay: 0.5s;
}

.shaft-load2{
    width: 100%;
    height: 2px;
    overflow: hidden;
    /* background: aqua; */
}
.shaft-load2 > div {
  float: left;
  height: 100%;
  width: 5px;
  margin-right: 1px;
  display: inline-block;
  background: #959595;
  -webkit-animation: loading2 1.5s infinite ease-in-out;
  -moz-animation: loading2 1.5s infinite ease-in-out;
  -o-animation: loading2 1.5s infinite ease-in-out;
  animation: loading2 1.5s infinite ease-in-out;
  -webkit-transform: scaleY(0.05) translateX(-5px);
  -moz-transform: scaleY(0.05) translateX(-5px);
  -ms-transform: scaleY(0.05) translateX(-5px);
  -o-transform: scaleY(0.05) translateX(-5px);
  transform: scaleY(0.05) translateX(-5px);
}

@-webkit-keyframes loading2 {
  10% {
    background: #ffffff;
  }
  15% {
    -webkit-transform: scaleY(1.2) translateX(10px);
    -moz-transform: scaleY(1.2) translateX(10px);
    -ms-transform: scaleY(1.2) translateX(10px);
    -o-transform: scaleY(1.2) translateX(10px);
    transform: scaleY(1.2) translateX(10px);
    background: #ffffff;
  }
  90%, 100% {
    -webkit-transform: scaleY(0.05) translateX(-5px);
    -moz-transform: scaleY(0.05) translateX(-5px);
    -ms-transform: scaleY(0.05) translateX(-5px);
    -o-transform: scaleY(0.05) translateX(-5px);
    transform: scaleY(0.05) translateX(-5px);
  }
}











/*文章里的链接识别*/
.sh-wz-nr-url{
    color: var(--thetitle);
    pointer-events: all;
    text-decoration: underline;
    -webkit-text-decoration: underline;
    word-wrap:break-word;  
    word-break:break-all;  
    overflow: hidden;
}

/*评论里面链接设别*/
.sh-wz-nr-pl-url{
    color: var(--thetitle);
    text-decoration: underline;
    -webkit-text-decoration: underline;
    pointer-events: all;
    word-wrap:break-word;  
    word-break:break-all;  
    overflow: hidden;
}






/* 发布页面 样式*/
.edit-main{
    background: var(--cobg);
    /*height: 100%;*/
    /*max-height: 100%;*/
    padding-bottom: 20px;
    flex: 1;
}
.sh-main-head-top-right-s-fas{
    width: 60px;
    height: 35px;
    background: rgb(128, 73, 0);
    display: flex;
    justify-content: center;
    align-items: center;
    margin-right: 15px;
    background: var(--theme);
    color: rgb(255, 255, 255);
    border-radius: 4px;
}
.sh-main-head-top-right-s-fas>button{
    width: 100%;
    height: 100%;
    font-size: 14px;
    outline: none;
    border: none;
    /*color: var(--cobg);*/
    color: rgb(255, 255, 255);
    cursor: pointer;
    background: rgb(255,255,255,0);
}

.sh-cont-nr{
    width: 100%;
}
.sh-cont-nr-tzh{
    padding: 15px;
}
.sh-cont-nr-tzh>textarea{
    width: 100%;
    height: 120px;
    outline: none;
    resize: none;
    border: 0px solid #ced4da;
    font-family: inherit;
    font-size: 14px;
    max-height: 250px;
    min-height: 120px;
    background: var(--cobg);
    color: var(--textqh);
}

/*设置滚动条宽度*/
.sh-cont-nr-tzh>textarea::-webkit-scrollbar {
        width: 4px;
    }
/*设置外层滑动槽的颜色*/
.sh-cont-nr-tzh>textarea::-webkit-scrollbar-track {
    background-color: rgb(0,0,0,0);
}
/*设置滑动条的颜色*/
.sh-cont-nr-tzh>textarea::-webkit-scrollbar-thumb {
    background-color: var(--gdwys);
    border-radius: 4px;
}





.sh-cont-dw{
    width: 100%;
    display: flex;
    align-items: center;
    margin-top: 10px;
}
.sh-cont-dw-tu{
    width: 35px;
    height: 35px;
    margin-left: 5px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
}
.sh-cont-dw>input{
   width: 100%;
    height: 25px;
    -webkit-border-radius: 2px;
    border-radius: 4px;
    padding: 5px 0px;
    border: none;
    outline: none;
    font-size: 14px;
    /* margin-left: 10px; */
    /* font-size: 14px; */
    /* background: var(--backbg); */
    background: var(--cobg);
    color: var(--textqh);
    margin-right: 10px;
}

.sh-cont-wz{
    width: 100%;
    display: flex;
    align-items: center;
    margin-top: 10px;
}
.sh-cont-wz-lx{
    /*width: 187px; */
    display: flex;
    align-items: center;
    height: 40px;
    /* background: var(--theme); */
    border-radius: 4px;
    /* color: var(--cobg); */
    color: #707070;
    margin-left: 5px;
}
.sh-cont-wz-lx>input{
    -webkit-appearance: auto;
    width: 16px;
    height: 16px;
    margin-left: 10px;
}
.sh-cont-wz-lx>span{
    margin-left: 5px;
    margin-right: 5px;
    font-size: 15px;
}


.sh-cont-nr-kg{
    width: 100%;
    display: flex;
}
.sh-cont-nr-kg-bqkg{
    width: 35px;
    height: 35px;
    background: var(--fgxys);
    margin-left: 15px;
    border-radius: 4px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
}


.sh-cont-nr-kg-ggkg{
    width: 35px;
    height: 35px;
    background: var(--fgxys);
    margin-left: 5px;
    border-radius: 4px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
}

.sh-cont-nr-kg-lxqh{
    width: 35px;
    height: 35px;
    background: var(--themetm);
    margin-left: 5px;
    border-radius: 4px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
}

.sh-cont-nr-kg-twl{
    width: 35px;
    height: 35px;
    background: var(--fgxys);
    margin-left: 5px;
    border-radius: 4px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
}


.sh-cont-nr-kg-yxplkg{
    width: 35px;
    height: 35px;
    background: var(--fgxys);
    margin-left: 5px;
    border-radius: 4px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
}




.sh-cont-gg{
    width: 100%;
    margin-top: 10px;
    display: none;
}

.sh-cont-gg>input{
    width: 100%;
    height: 25px;
    border-radius: 4px;
    padding: 5px 8px;
    border: none;
    outline: none;
    /* border: 1px #e7e7e7 solid; */
    margin-left: 10px;
    font-size: 14px;
    margin-left: 15px;
    font-size: 14px;
    background: var(--backbg);
    color: var(--textqh);
    margin-right: 15px;
}




.sh-cont-img{
    width: 100%;
    margin-top: 10px;
    display: flex;
    flex-direction: column;
}
.sh-cont-imgul{
    display: none;
    margin-bottom: 5px;
}
.form-controllul{
    flex: 1;
    height: 90px;
    outline: none;
    border: 0px solid rgb(206, 212, 218);
    font-family: inherit;
    font-size: 14px;
    resize: none;
    background: var(--backbg);
    color: var(--textqh);
    margin: 0px 15px;
    padding: 5px 8px;
    border-radius: 4px;
    min-height: 25px;
    max-height: 90px;
}
/*设置滚动条宽度*/
.form-controllul::-webkit-scrollbar {
        width: 4px;
    }
/*设置外层滑动槽的颜色*/
.form-controllul::-webkit-scrollbar-track {
    background-color: rgb(0,0,0,0);
}
/*设置滑动条的颜色*/
.form-controllul::-webkit-scrollbar-thumb {
    background-color: var(--gdwys);
    border-radius: 4px;
}










.sh-cont-yy{
    width: 100%;
    display: none;
    flex-direction: column;
    /* margin-bottom: 15px; */
    margin-top: 10px;
    margin-bottom: 10px;
}
.sh-cont-yy>p{
    margin-left: 15px;
    margin-right: 15px;
    font-size: 13px;
    color: var(--protext);
    background: var(--protextbg);
    width: fit-content;
    padding: 2px 4px;
    border-radius: 4px;
    margin-top: 5px;
}
.sh-cont-yy>span{
    display: flex;
    justify-content: center;
    padding: 0 15px;
}
.sh-cont-yy>span>input{
    width: 100%;
    height: 25px;
    -webkit-border-radius: 2px;
    border-radius: 4px;
    padding: 5px 8px;
    border: none;
    outline: none;
    /* border: 1px #e7e7e7 solid; */
    /* margin-left: 15px; */
    font-size: 14px;
    background: var(--backbg);
    color: var(--textqh);
    /* margin-right: 15px;*/
}
.sh-cont-yy>span>p{
    margin: 0 1px;
}
.sh-cont-yy-fg{
    margin: 1px 0;
}




.sh-cont-wz-gg{
    width: 100%;
    display: flex;
    flex-direction: column;
    /* align-items: center; */
    margin-top: 10px;
}
.sh-cont-wz-gg>p{
    margin-left: 10px;
    font-size: 15px;
    margin-bottom: 5px;
}
.sh-cont-wz-gg-an{
    display: flex;
    align-items: center;
    width: 144px;
    height: 40px;
    background: var(--theme);
    color: var(--cobg);
    border-radius: 4px;
    margin-left: 15px;
}
.sh-cont-wz-gg-an>input{
    -webkit-appearance: auto;
    width: 16px;
    height: 16px;
    margin-left: 10px;
}
.sh-cont-wz-gg-an>span{
    margin-left: 5px;
    margin-right: 5px;
    font-size: 15px;
}






.sh-cont-sp{
    width: 100%;
    margin-top: 10px;
    display: none;
}
.sh-cont-sp>input {
    width: 100%;
    height: 25px;
    -webkit-border-radius: 2px;
    border-radius: 4px;
    padding: 5px 8px;
    border: none;
    outline: none;
    /* border: 1px #e7e7e7 solid; */
    margin-left: 15px;
    font-size: 14px;
    background: var(--backbg);
    color: var(--textqh);
    margin-right: 15px;
}






input[type="radio"]+label::before {
     content: " ";
     display: inline-block;
     vertical-align: middle;
     width: 16px;
     height: 16px;
     border-radius: 50%;
     border: 1px solid var(--texths);
     margin-right: 0.2rem;
     box-sizing: border-box;
     margin-top: -0.2rem;
}

input[type="radio"]:checked+label::before {
     background-color: var(--texths);
     background-clip: content-box;
     border: 1px solid var(--texths);
     padding: 0.1rem;
     box-sizing: border-box;
}
input[type="radio"] {
     /*position: absolute;
     clip: rect(0, 0, 0, 0);*/
     margin-right: -16px;
     opacity: 0;
}
.sexStyle{
    pointer-events: none;
    font-size: 15px;
}

input[type="file"] {
    color: transparent;
    width: 100px;
    height: 100px;
    background: #f0f8ff00;
    opacity: 0;
    cursor: pointer;
}

.filsp{
    margin-left: 15px;
    position: relative;
    display: none;
    justify-content: center;
    align-items: center;
    text-align: center;
    background-color: var(--backbg);
    border-radius: 2px;
    box-sizing: border-box;
    width: 100px;
    height: 100px;
    line-height: 100px;
    margin-top: 10px;
}
.filsp>span{
    position: absolute; 
    /*left: 38px;*/
    font-size: 28px; 
    color: rgb(140, 147, 157);
}


.sh-cont-video{
    max-width: 100%;
    max-height: 280px;
    margin-top: 10px;
    margin-left: 15px;
    margin-right: 15px;
    display: none;
}



/*发布页面的图片选择后的预览板块*/
.cupload-image-list{
    width:70%;
}

/*.cupload-image-list-wk{
    max-height: 310px;
    overflow-x: hidden;
    overflow-y: scroll;
    overscroll-behavior-y: contain;
}
/*设置滚动条宽度*/
/*.cupload-image-list-wk::-webkit-scrollbar {
        width: 4px;
    }
/*设置外层滑动槽的颜色*/
/*.cupload-image-list-wk::-webkit-scrollbar-track {
    background-color: rgb(0,0,0,0);
}
/*设置滑动条的颜色*/
/*.cupload-image-list-wk::-webkit-scrollbar-thumb {
    background-color: var(--gdwys);
    border-radius: 4px;
}
*/
.cupload-image-box{
    width: 100px;
    height: 100px;
    margin-right: 5px;
}


.cupload-upload-box{
    justify-content: center;
    align-items: center;
}




.edit-detu{
    width: 100%;
    height: 80px;
    display: flex;
    justify-content: center;
    align-items: center;
    position: fixed;
    bottom: 0;
    right: 0;
}
.edit-detu-wk{
    width: 100%;
    max-width: 550px;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    background: var(--protext);
}
.edit-detu-wk-xx{
    display: flex;
    flex-direction: column;
    align-items: center;
}
.edit-detu-wk-xx>i{
    color: var(--iconbs);
    font-size: 16px;
}
.edit-detu-wk-xx>span{
    color: var(--iconbs);
    font-size: 16px;
    margin-top:5px;
}


 











/*文章详情查看页面UI*/
.zan2{
    display: flex;
    /*align-items: center;*/
}
.sh-zanp-zan-right2{
    flex-wrap: wrap;
    margin-top: 10px;
}
.sh-zanp-zan-right2>li{
    width: 30px;
    height: 30px;
    margin-right: 5px;
    margin-bottom: 5px;
}
.sh-zanp-zan-right2>li>img{
    width: 100%;
    height: 100%;
    border-radius: 2px;
    object-fit: cover;
}


.sh-zanp-zan-left2{
    margin-top: 10px;
}


.sh-zanp-pl2{
    background: rgb(255,255,255,0);
    margin-top: 0px;
}
.sh-zanp-pl2>li{
    display: flex;
}
.sh-zanp-pl-tx{
    display: flex;
    /*align-items: center;*/
    justify-content: center;
}
.sh-zanp-pl-tx>img{
    width: 30px;
    height: 30px;
    object-fit: cover;
}


.sh-zanp-pl-n-mz>span{
    color: var(--texths);
    font-size: 12px;
}

.sh-zanp-pl-n2{
    margin-left: 10px;
}



.sh-dz-z{
    display: flex;
    background: var(--backbg);
    margin-top: 1px;
    border-radius: 4px;
}

.sh-zanp-pl3>li{
    margin-left: 0px;
    margin-top: 5px;
    margin-bottom: 5px;
    border-bottom: 1px solid var(--fgxys);
    padding-bottom: 4px;
    justify-content: initial;
    line-height: initial;
}

.sh-zanp-pl-tx{
    width: 30px;
    height: 30px;
    pointer-events: none;
}
.sh-zanp-pl-tx>img{
    border-radius: 2px;
    pointer-events: none;
}

.sh-zanp-pl-n2>span{
    font-size: 14px;
}

.sh-content2{
    border-bottom: 0px solid var(--fgxys);
}


.sh-view-head-top-left-s{
    width: 40px;
    height: 40px;
    /* background: rgb(128, 73, 0); */
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    pointer-events: none;
}
.setup-view-title{
    color: #ffffff;
}













/*消息列表右侧封面样式*/
.sh-xxliebfm{
    width: 60px;
    height: 60px;
    margin-right: 15px;
    pointer-events: none;
    overflow: hidden;
    border-radius: 4px;
    position:relative;
    flex-shrink: 0;
    /*-webkit-appearance: none;
    -webkit-backface-visibility: hidden;
    -webkit-transform: translate3d(0, 0, 0);*/
}
.sh-xxliebfm>img{
    width: 100%;
    height: 100%;
    object-fit: cover;
}
.delmes{
    width: 100%;
    height: 100%;
    background: var(--protext);
    color: white;
    font-size: 15px;
    position: absolute;
    top: 0px;
    left: 0px;
    display: none;
    justify-content: center;
    align-items: center;
    pointer-events: all;
}

.sh-xxliebwb{
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    color: var(--texths);
    background: var(--backbg);
}
.sh-xxliebwb>span{
    /*width: 80%;
    font-size: 12px;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
    overflow: hidden;*/
    width: 80%;
    /*height: 80%;*/
    font-size: 12px;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
    overflow: hidden;
    word-wrap: break-word;
    word-break: break-all;
}






/*au*/
.sh-lan-auth{
    position: fixed;
    left: 0px;
    bottom: 120px;
    height: 40px;
    background: #2997f7;
    display: flex;
    align-items: center;
    z-index: 999;
    color: var(--theme);
    /*background: rgb(7,193,96,0.1);*/
    border-radius: 0px 4px 4px 0px;
    background: rgb(7,193,96,0.1);
    backdrop-filter: saturate(180%) blur(20px);
    -webkit-backdrop-filter: saturate(180%) blur(20px)
}
.sh-lan-auth>p{
    font-size: 14px;
    margin-left: 10px;
    margin-right: 10px;
}














.setup-main-select{
    height: 38px;
    border-color: #ebebeb;
    -webkit-border-radius: 2px;
    border-radius: 2px;
    padding: 5px 12px;
    line-height: inherit;
    /*-webkit-transition: 0.2s linear;
    transition: 0.2s linear;*/
    -webkit-box-shadow: none;
    box-shadow: none;
    outline: none;
    margin: 0px 15px 5px 15px;
}















/*个人主页背景上传器ui*/
.sh-resces-gl{
    width: 100%;
    max-width: 550px;
    height: 100%;
    position: fixed;
    top: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    background: #00000026;
    z-index: 10;
    -webkit-animation:move_2 0.25s;
    backdrop-filter: saturate(100%) blur(2px);
    -webkit-backdrop-filter: saturate(100%) blur(2px);
}
.sh-resces-gl-k{
    width: 90%;
    display: flex;
    flex-direction: column;
    border-radius: 4px;
    background: var(--cobg);
    -webkit-animation:move_1 0.25s;
}
.sh-resces-gl-s{
    width: 100%;
    height: 50px;
    display: flex;
    justify-content: center;
    align-items: center;
}
.sh-resces-gl-s>span{
    font-size: 15px;
    /*font-weight: bold;*/
    margin-left: 10px;
    margin-right: 10px;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    overflow: hidden;
    color: var(--textqh);
}
.sh-resces-gl-z{
    width: 100%;
    min-height: 150px;
    display: flex;
    justify-content: center;
    align-items: center;
}
.sh-resces-gl-z-formup{
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;
    background: var(--backbg);
    border-radius: 4px;
    box-sizing: border-box;
    width: 90%;
    height: 90%;
    min-height: 150px;
}
.sh-resces-gl-z-formup>img{
   width: 100%;
    height: 100%;
    display: none;
    object-fit: cover;
    object-position: center;
    border-radius: 4px;
    max-height: 500px;
}
.sh-resces-gl-z-formup>span{
    position: absolute;
    /* left: 38px; */
    font-size: 28px;
    color: rgb(140, 147, 157);
}
.sh-resces-gl-z-formup>input{
    width: 100%;
    height: 100%;
    min-height: 150px;
}
.sh-resces-gl-x{
    width: 100%;
    height: 50px;
    display: flex;
    justify-content: center;
    align-items: center;
}
.sh-resces-gl-x>input{
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    background: var(--cobg);
    cursor: pointer;
    border: none;
    outline: none;
    color: var(--thetitle);
    font-size: 14px;
    border-radius: 0px 0px 4px 4px;
}










/*设置界面LOGO图像ui*/
.setup-main-lieb-logtx-scr1{
   width: 50px;
    height: 50px;
    margin: 0px 15px 5px 15px;
    border: 1px solid #e7e7e7;
    object-fit: cover;
}
.setup-main-lieb-logtx-kj{
    width: 100%;
    display: flex;
    align-items: center;
}
.setup-main-lieb-logtx-srk{
    flex: 1;
    margin: 0px 10px 5px 15px;
    height: 38px;
    -webkit-border-radius: 2px;
    border-radius: 2px;
    padding: 0px 12px;
    border: none;
    outline: none;
    border: 1px #e7e7e7 solid;
}
.setup-main-lieb-logtx-tj{
    width: 48px;
    height: 38px;
    display: flex;
    justify-content: center;
    align-items: center;
    background: var(--theme);
    border-radius: 4px;
    margin-bottom: 5px;
    margin-right: 15px;
    /* margin-left: 15px; */
    /* margin-bottom: 15px; */
    cursor: pointer;
    border: none;
    outline: none;
    color: var(--cobg);
    font-size: 14px;
}


.sh-setup-formup-logtx{
    margin-left: 15px;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;
    background: var(--backbg);
    border-radius: 2px;
    box-sizing: border-box;
    width: 60px;
    height: 60px;
    /* line-height: 100px; */
    margin-bottom: 10px;
}
.sh-setup-formup-logtx>img{
    width: 60px;
    height: 60px;
    display: none;
    object-fit: cover;
    object-position: center;
}
.sh-setup-formup-logtx>span{
    position: absolute;
    font-size: 23px;
    color: rgb(140, 147, 157);
}
.sh-setup-formup-logtx>input{
    width: 60px;
    height: 60px;
}



















/* i 图标板块*/
.ri-sx{
    font-size: 20px;
    color: var(--iconbs);
}
.ri-sxh{
    font-size: 20px;
    color: var(--iconhs);
}

.ri-z-sx{
    font-size: 20px;
    color: var(--iconbs);
}
.ri-z-sxh{
    font-size: 20px;
    color: var(--iconhs);
}


/*灰色i图标区-首页信息删除按钮*/
.ri-sxhs{
    font-size: 20px;
    color: var(--iconhs);
}
.ri-sxhsh{
    font-size: 20px;
    color: #FF3B3B;
}


/*灰色取消图标-首页*/
.ri-sxhqx{
    font-size: 20px;
    color: var(--iconhs);
}

/*文章喜欢图标 蓝色*/
.ri-sxwzlike{
    font-size: 20px;
    color: var(--thetitle);
}

/*文章广告图标*/
.ri-sxwzgg{
    color: var(--thetitle);
    font-size: 20px;
    margin-right: 2px;
}

/*点赞按钮图标 白色*/
.ri-sxdzlike{
    font-size: 18px;
    color: var(--iconbs);
    pointer-events: none;
}
/*点赞按钮图标 红色*/
.ri-sxdzlikehs{
    font-size: 18px;
    color: #ff5858;
    pointer-events: none;
}
/*评论按钮图标 白色*/
.ri-sxdzcomm{
    font-size: 18px;
    color: var(--iconbs);
    pointer-events: none;
}
/*评论按钮图标 蓝色*/
.ri-sxdzcommls{
    font-size: 20px;
    color: var(--thetitle);
}

/*删除文章按钮- home个人主页*/
.ri-sxdewz{
    font-size: 20px;
    color: var(--thetitle);
    margin-right: 5px;
    cursor: pointer;
}
/*置顶文章按钮- home个人主页*/
.ri-sxzdwz{
    font-size: 20px;
    color: var(--thetitle);
    cursor: pointer;
}
/*隐私文章按钮- home个人主页*/
.ri-sxyswz{
    font-size: 20px;
    color: var(--thetitle);
    cursor: pointer;
    margin-right: 5px;
}

/*表情选择 蓝色*/
.ri-sxbqxz{
    font-size: 22px;
    color: var(--thetitle);
    cursor: pointer;
    margin-right: 5px;
}
/*表情选择 绿色*/
.ri-sxbqxzls{
    font-size: 22px;
    color: var(--theme);
    cursor: pointer;
    margin-right: 5px;
}


/*发布页表情*/
.ri-sxfbbq{
    font-size: 20px;
    color: var(--iconhs);
}
/*发布页表情 绿色*/
.ri-sxfbbqls{
    font-size: 20px;
    color: var(--theme);
}

/*发布页定位*/
.ri-sxgps{
    font-size: 18px;
    color: var(--iconhs);
}
/*home个人主页 背景图修改图标*/
.ri-sxbxt{
    font-size: 20px;
    color: var(--textbs);
}

/*首页登录头像图标*/
.ri-sxzytx{
    font-size: 20px;
    color: var(--iconbs);
}
.ri-sxzytxh{
    font-size: 20px;
    color: var(--iconhs);
}











/*阿里系列 iconfont 图标i标签属性*/
/*白色*/
.al-sxb{
    font-size: 20px;
    color: var(--iconbs);
}
.al-sxb2{
    font-size: 20px;
    color: var(--iconbs);
}

/*灰色*/
.al-sxbh{
    font-size: 20px;
    color: var(--iconhs);
}
.al-sxb2h{
    font-size: 20px;
    color: var(--iconhs);
}


.al-sxbh-admin{
    font-size: 20px;
    color: var(--iconhs);
    margin-right: 20px;
    margin-top: 20px;
    cursor: pointer;
}


.al-sxbh-setup1{
    font-size: 15px;
    color: var(--iconhs);
}
.al-sxbh-setup{
    font-size: 15px;
    color: var(--iconhs);
    margin-right: 15px;
}








/*版权*/
.sh-copyright{
    position: fixed;
    bottom: 20px;
    left: 40px;
    /*z-index: -1;*/
}
.sh-copyright-banquan{
    color: rgb(119 119 119 / 50%);
    font-size: 12px;
}
.sh-copyright-banquan>a{
    color: rgb(119 119 119 / 50%)!important;
    font-size: 12px!important;
}
.sh-copyright-beian{
    color: rgb(119 119 119 / 50%);
    font-size: 12px;
}














/*新音乐UI*/
.music-play {
            width: 100%;
            height: 80px;
            position: relative;
            border-radius: 4px;
            margin-top: 7px;
            margin-bottom: 7px;
        }

        .music-play-conten {
            width: 70%;
            max-width: 85%;
            height: 100%;
            display: flex;
            align-items: center;
            /*background: rgb(226, 226, 226, 0.7);*/
            /*background-image: linear-gradient(88deg, #fff,rgb(226, 226, 226, 0.5));*/
            border-radius: 4px;
            transition:all 0.2s;
            position: relative;
            overflow: hidden;
            background: var(--imgbgy);
            /*filter: brightness(1);*/
        }

        .music-play-conten-left {
            width: 75px;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            border-radius: 4px;
            /*margin-left: 2px;*/
            z-index: 0;
        }

        .music-play-conten-left-bg {
            width: 100%;
            height: 100%;
            background-size: cover;
            border-radius: 4px 0px 0px 4px;
        }

        .music-play-conten-right {
            flex: 1;
            height: 100%;
            /* background: aquamarine; */
            /*border-radius: 4px;*/
            border-radius: 0 4px 4px 0;
            display: flex;
            align-items: center;
            justify-content: space-between;
            z-index: 0;
            
        }

        .music-play-conten-right-z {
            width: 80%;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            /*z-index: 1;*/
        }

        .music-play-conten-right-z>span {
            margin-left: 10px;
            margin-right: 5px;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 1;
            overflow: hidden;
        }

        .conten-right-z-bt {
            font-size: 14px;
            /*color: #181818;*/
            color: var(--textbs);
            margin-bottom: 2px;
        }

        .conten-right-z-nr {
            font-size: 12px;
            /*color: #666;*/
            color: var(--textbs);
        }

        .music-play-conten-right-y {
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            /*z-index: 1;*/
        }

        .conten-right-y-tb {
            width: 21px;
            height: 21px;
            border-radius: 360px;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-right: 18px;
            /* background: #07c160; */
            /* background: rgb(0,0,0,0.15); */
            /*border: 1px solid #525252;
            backdrop-filter: saturate(180%) blur(20px);
            -webkit-backdrop-filter: saturate(180%) blur(20px);*/
            cursor: pointer;
        }

        .conten-right-y-tb>i {
            font-size: 20px;
            /*color: #525252;*/
            color: var(--textbs);
            pointer-events: none;
            
        }




/*游客信息ui*/
.sh-plk-yk{
    width: 100%;
    /*display: flex;*/
    justify-content: space-between;
    /* margin-bottom: 8px; */
    display: none;
    /*background: var(--cobg);*/
    background: rgb(255,255,255,0);
    border-radius: 4px 4px 0 0;
    margin-top: 8px;
}
.sh-plk-yk-z{
    width: 100%;
    height: 35px;
    /*background: var(--fgxys);*/
    border-radius: 4px;
    display: flex;
    justify-content: center;
    align-items: center;
    /*background: var(--cobg);*/
    background: var(--backbg);
}
.sh-plk-yk-z>input{
    width: 100%;
    height: 100%;
    border: 0;
    outline: none;
    /*background: var(--dbztlysh);*/
    background: rgb(255,255,255,0);
    padding: 0px 8px;
    font-size: 12px;
}
.sh-plk-yk-zz{
    width: 100%;
    height: 35px;
    /*background: var(--fgxys);*/
    border-radius: 4px;
    margin: 0px 8px;
    display: flex;
    justify-content: center;
    align-items: center;
    /*background: var(--cobg);*/
    background: var(--backbg);
}
.sh-plk-yk-zz>input{
    width: 100%;
    height: 100%;
    border: 0;
    outline: none;
    background: var(--dbztlysh);
    padding: 0px 5px;
    font-size: 12px;
}

.sh-plk-yk-z>input::placeholder {
  color: #8b8b8b;
}
.sh-plk-yk-zz>input::placeholder {
  color: #8b8b8b;
}






/*右下角悬浮菜单*/
.sh-menu{
    position: fixed;
    bottom: 50px;
    right: 20px;
    width: 40px;
    display: none;
    flex-direction: column;
    align-items: center;
    z-index: 1;
    -webkit-animation: move_1 0.20s;
}
.sh-menu-k{
    width: 100%;
    height: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    background: var(--menu);
    border-radius: 4px;
    margin-top: 8px;
    cursor: pointer;
    backdrop-filter: saturate(180%) blur(20px);
    -webkit-backdrop-filter: saturate(180%) blur(20px);
}
.sh-menu-k>i{
    color: var(--iconbs);
    font-size: 20px;
    pointer-events: none;
}












/*独立音乐播放器ui*/
.musicbc {
            position:fixed;
            bottom: 200px;
            right: -800px;
            width: 135px;
            height: 50px;
            display: grid;
            max-width: 150px;
            background: var(--dbztlys);
            touch-action: none;
            border-radius: 360px 0 0 360px;
            align-items: flex-end;
            z-index: 99;
            transition:all 0.5s;
            backdrop-filter: saturate(180%) blur(20px);
            -webkit-backdrop-filter: saturate(180%) blur(20px);
            box-shadow: 0 0 10px rgb(0,0,0,0.05);
            overflow: hidden;
        }



        .musicimg {
            width: 50px;
            height: 50px;
        }

        .musicimg>img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 360px;
            animation: rotate 5s infinite linear;
        }

        .musiccz {
            height: 50px;
            margin-left: 15px;
            display: flex;
            align-items: center;
        }
        .musiccz-btn{
            margin-right: 15px;
            cursor:pointer;
        }

        .musicbcbj>img{
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border-radius: 360px 0 0 360px;
            filter: blur(10px);
            /* transform: scale(10); */
            object-fit: cover;
        }

        
        .mimg{
            filter: blur(30px);
            object-fit: cover;
        }
        .mimg,.muka{
            grid-column: 1;
            grid-row: 1;
            width: 150%;
            height: 50px;
            display: flex;
            z-index: -1;
        }
        .muka{
            z-index: 0;
        }
        
        
/* 定义动画关键帧 */
@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

#myDiv {
  transition: transform 0.5s ease;
}









/*新版个人主页*/
.sh-homecontent {
    width: 100%;
    display: flex;
    flex-direction: column;
    margin-top: 30px;
    /*background: wheat;*/
}

.sh-homecontent-timed {
    margin-top: 40px;
    margin-bottom: 5px;
    padding: 0 10px;
    color: var(--textqh);
}

.sh-homecontent-timed-n {
    font-size: 14px;
    font-weight: 400;
}

.sh-homecontent-lie {
    width: 100%;
    display: flex;
    margin-bottom: 5px;
    /*border-bottom: 1px solid var(--fgxys);*/

}

.sh-homecontent-left {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    /* align-items: center; */
    margin-right: 15px;
    max-width: 64px;
    min-width: 64px;
    margin-left: 25px;
}

.homecontent-left-time-h {
    font-size: 20px;
    font-weight: bold;
    margin-right: -4px;
    /*color: var(--textqh);*/
    color: rgb(0 0 0 / 0%);
}

.homecontent-left-time-y {
    font-size: 12px;
    /*color: var(--textqh);*/
    color: rgb(0 0 0 / 0%);
}

.sh-homecontent-left-time-dw {
    max-width: 80px;
    font-size: 12px;
    margin-top: 5px;
    color: var(--texths);
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
    word-wrap: break-word;
    word-break: break-all;
}

.sh-homecontent-right-wk {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    margin-right: 25px;
}

.sh-homecontent-right-lie {
    width: 100%;
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
    /*margin-bottom: 10px;*/
    cursor: pointer;
}

.homecontent-right-tw {
    width: 80px;
    height: 80px;
    /*background: violet;*/
    display: flex;
    justify-content: center;
    align-items: center;
    margin-right: 5px;
    flex-shrink: 0;
}

.homecontent-right-tw-img {
    width: 100%;
    height: 100%;
    display: grid;
    grid-template-columns: repeat(2, 49.5%);
    grid-template-rows: repeat(2, 49.5%);
    /* column-gap:24px;
row-gap:24px; */
    gap: 1px;
    position:relative;
}

.homecontent-right-tw-img .homecontent-right-tw-img-wk:nth-child(3)~.homecontent-right-tw-img-wk:first-child {
    grid-row: 1 / 3;
}

.homecontent-right-tw-img-wk {
    /* width: 80px;
    height: 80px; */
    width: 100%;
    height: 100%;
    max-height: 80px;
    background: var(--imgbgy);
}

.homecontent-right-tw-img-wk>img {
    width: 100%;
    height: 100%;
    max-width: 80px;
    max-height: 80px;
    object-fit: cover;
}


.homecontent-right-nr {
    width: 100%;
    min-height: 80px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: flex-start;
}
.homecontent-right-nr>i{
    color: var(--iconhs);
}
.homecontent-right-nr-text {
    font-size: 14px;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
    overflow: hidden;
    word-wrap:break-word;  
    word-break:break-all;  
    color: var(--textqh);
}
.homecontent-right-nr-text>a{
    color: var(--thetitle);
}
.homecontent-right-nr-textjw {
    font-size: 14px;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
}
.homecontent-right-nr-textjw>a{
    color: var(--thetitle);
}
.homecontent-right-nr-tus {
    font-size: 12px;
    margin-top: 5px;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    overflow: hidden;
    color: var(--texths);
}

.homecontent-right-tw-video {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    position:relative;
}

.homecontent-right-tw-videoau {
    width: 100%;
    height: 100%;
    min-height: 80px;
    object-fit: cover;
    background: rgb(62 62 62);
}



.sh-homecontent-right-lie-musicwk {
    width: 100%;
    display: flex;
    flex-direction: column;
    background: var(--backbg);
    /*margin-bottom: 10px;*/
    position:relative;
    cursor: pointer;
}
.sh-homecontent-right-lie-musicwk>i{
    color: var(--iconhs);
}

.sh-homecontent-right-lie-music-title {
    font-size: 14px;
    margin: 5px 5px 0 5px;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    overflow: hidden;
    color: var(--textqh);
}
.sh-homecontent-right-lie-music-title>a{
    color: var(--thetitle);
}
.sh-homecontent-right-lie-music {
    padding: 5px 0;
    margin-bottom: 0;
}

.homecontent-right-tw-music {
    width: 40px;
    height: 40px;
    margin-left: 5px;
}

.homecontent-right-tw-img-wk-music {
    max-height: 40px;
    position:relative;
}
.homecontent-right-tw-img-wk-music>i{
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: var(--iconbs);
}
.homecontent-right-tw-img-wk-music>img {
    max-width: 40px;
    max-height: 40px;
}

.homecontent-right-nr-music {
    min-height: 10px;
}

.homecontent-right-nr-text-music-p {
    font-size: 14px;
}

.homecontent-right-nr-text-music {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    overflow: hidden;
    color: var(--textqh);
}
.homecontent-right-nr-text-music>a{
    color: var(--thetitle);
}









/* view详情页面设置弹窗*/
.sh-view-set{
    width: 100%;
    height: 100%;
    position: fixed;
    bottom: 0;
    right: 0;
    display: flex;
    justify-content: center;
    z-index: 150;
    pointer-events: none;
    display: none;
}
.sh-view-set-wk{
    width: 100%;
    height: 100%;
    max-width: 550px;
    background: rgb(0 0 0 / 30%);
    display: flex;
    align-items: flex-end;
    pointer-events: all;
    /*display: none;*/
    -webkit-animation: move_2 0.20s;
}
.sh-view-set-wk-con{
    width: 100%;
    background: var(--bodys);
    display: flex;
    flex-direction: column;
    align-items: center;
    overflow: hidden;
    border-radius: 8px 8px 0 0;
    /*-webkit-animation: move_4 0.20s;*/
}
.sh-view-set-wk-con-title{
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    background: var(--cobg);
    cursor: pointer;
}
.sh-view-set-wk-con-title>span{
    margin: 15px;
    font-size: 15px;
    pointer-events: none;
    color: var(--textqh);
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    overflow: hidden;
    word-wrap:break-word;  
    word-break:break-all; 
}
/*.view-set-wk-con-title-fgx{
    width: 100%;
    height: 1px;
    background: var(--backbg);
}*/












/* 动画区域 */
@keyframes myfirsjss
{
    0%  {bottom:-500px;}
    50% {bottom:0px;}
    100% {bottom:0px;}

}


/*input编辑框选中后改变颜色*/
.setup-main-lieb-content>input:focus {
    /*box-shadow: 0px 0px 5px #07c160;*/
    border: 1px #07c160 solid;
}
.setup-main-lieb-content>input{
    -webkit-transition:all 0.3s;
    transition: all 0.3s;
}

.setup-main-lieb-content>textarea:focus {
    /*box-shadow: 0px 0px 5px #07c160;*/
    border: 1px #07c160 solid;
}
.setup-main-lieb-content>textarea{
    -webkit-transition:all 0.3s;
    transition: all 0.3s;
}

.setup-main-lieb-logtx-srk:focus {
    /*box-shadow: 0px 0px 5px #07c160;*/
    border: 1px #07c160 solid;
}
.setup-main-lieb-logtx-srk{
    -webkit-transition:all 0.3s;
    transition: all 0.3s;
}





/*.sh-content-right-time-right-right:hover{
    -webkit-filter: brightness(90%); /*考虑浏览器兼容性：兼容 Chrome, Safari, Opera */
    /*filter: brightness(90%);
}*/

.sh-content-right-time-right-left-z:hover{
    background: #474c4d;
    border-radius: 4px 0px 0px 4px;
}

.sh-content-right-time-right-left-y:hover{
    background: #474c4d;
    border-radius: 0px 4px 4px 0px;
}

.sh-main-head-top-left-s:hover{
    background: var(--zsgfh);
    border-radius: 4px;
}
.sh-main-head-top-right-s:hover{
    background: var(--zsgfh);
    border-radius: 4px;
}
.sh-news-con-lie:hover{
    background: var(--backbg);
}
.sh-link-con-lie:hover{
    background: var(--backbg);
}
.sh-main-head-top-left-mu:hover{
    background: var(--zsgfh);
    border-radius: 4px;
}




/*.sh-resces-gl-k{
    -webkit-animation:move_1 0.25s;
}*/
/*.sh-resces-gl{
    -webkit-animation:move_2 0.25s;
}*/


@-webkit-keyframes move_1{
    0%{ -webkit-transform:translateY(15px); opacity:0;}
}


@-webkit-keyframes move_2{
    0%{ opacity:0;}
}


@-webkit-keyframes move_3{
    0%{ -webkit-transform:translateY(-25px); opacity:0;}
}




/*登录框入场动画调用*/
.move_4{
    -webkit-animation:move_4 0.20s;
    animation-fill-mode:forwards;
}
/*登录框入场动画*/
@-webkit-keyframes move_4{
    0% {
        -webkit-transform:translateY(100%);
      }

      100%{
          -webkit-transform:translateY(0);
          visibility:visible;
      }
}
/*登录框退场动画调用*/
.move_4t{
    -webkit-animation:move_4t 0.20s;
    animation-fill-mode:forwards;
}
/*登录框退场动画*/
@-webkit-keyframes move_4t{
    0% {
        -webkit-transform:translateY(0);
      }

      100%{
          -webkit-transform:translateY(100%);
          visibility: hidden;
      }
}




/*加载更多文章中提示动画*/
@keyframes colorChange {
  0% {
    opacity:1;
  }
  50% {
      opacity:0.4;
  }
  100% {
      opacity:1;
  }
}















/* 达到一定分辨率隐藏右边部分 */
@media screen and (max-width:560px) {
    .sh-copyright{
        display: none;
    }
    .sh-resces-gl{
        width:100%;
    }
    .music-play-conten{
        width: 75%;
    }
}


@media screen and (max-width:460px) {
    .cupload-image-list{
        width: 80%;
    }
    .music-play-conten{
        width: 100%;
    }
    .sh-right{
        width: 80%;
    }
    .sh-left{
        width: 80%;
    }
}
@media screen and (max-width:400px) {
    .cupload-image-list{
        width: 100%;
    }

}