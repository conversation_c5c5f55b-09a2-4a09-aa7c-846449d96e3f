# 内容筛选功能说明

## 功能概述

本次更新为朋友圈程序新增了内容筛选功能，用户可以通过关键词筛选整个页面显示的动态内容，只显示与筛选条件相关的用户发布的动态。

## 功能特点

1. **全局筛选**：筛选整个页面显示的动态内容，而非搜索特定用户
2. **多字段筛选**：支持按用户昵称、用户名、发布地点、动态内容进行筛选
3. **实时筛选**：筛选条件应用后立即生效，页面重新加载显示筛选结果
4. **筛选状态显示**：页面顶部显示当前筛选条件，方便用户了解筛选状态
5. **快捷标签**：提供常用关键词的快捷筛选标签
6. **筛选清除**：支持一键清除筛选条件，恢复显示所有内容

## 使用方法

### 基本使用
1. 在首页顶部导航栏右侧点击筛选图标（漏斗图标）
2. 在弹出的筛选界面中输入关键词
3. 点击"应用筛选"按钮
4. 页面将重新加载，只显示包含关键词的动态

### 快捷筛选
1. 在筛选界面中点击预设的快捷标签
2. 系统会自动填入对应的关键词
3. 点击"应用筛选"即可

### 清除筛选
1. 点击筛选状态栏右侧的"×"按钮
2. 或在筛选界面中点击"清除筛选"按钮
3. 页面将恢复显示所有动态内容

## 筛选范围

筛选功能会在以下字段中查找关键词：
- **用户昵称** (ptpname)：发布动态的用户昵称
- **用户名** (ptpuser)：发布动态的用户账号
- **发布地点** (ptpdw)：动态发布时的地理位置
- **动态内容** (ptptext)：动态的文字内容

## 技术实现

### 前端部分

#### 1. 界面组件
- **筛选按钮**：位于顶部导航栏，使用漏斗图标
- **筛选弹窗**：包含输入框、快捷标签、操作按钮
- **筛选状态栏**：显示当前筛选条件，支持快速清除

#### 2. JavaScript功能
```javascript
// 主要函数
kqContentFilter()     // 打开筛选弹窗
gbContentFilter()     // 关闭筛选弹窗
applyContentFilter()  // 应用筛选条件
clearContentFilter()  // 清除筛选条件
setFilterKeyword()    // 设置快捷关键词
```

### 后端部分

#### 1. 数据库查询
```sql
SELECT * FROM essay 
WHERE ptpaud<>'0' AND ptpaud<>'-1' AND ptpys<>'0' 
AND (ptpname LIKE '%关键词%' OR ptpuser LIKE '%关键词%' 
     OR ptpdw LIKE '%关键词%' OR ptptext LIKE '%关键词%') 
ORDER BY id DESC
```

#### 2. URL参数处理
- 筛选条件通过GET参数`filter`传递
- 支持与搜索功能(`so`参数)同时使用
- 加载更多功能也会保持筛选条件

## 文件修改清单

### 修改的文件
1. **index.php**
   - 添加筛选按钮
   - 添加筛选弹窗界面
   - 修改动态查询逻辑，支持筛选参数
   - 添加筛选状态显示

2. **assets/css/style.css**
   - 添加筛选弹窗样式
   - 添加筛选状态栏样式
   - 添加快捷标签样式
   - 响应式设计适配

3. **assets/js/index.js**
   - 添加筛选相关函数
   - 修改加载更多函数，支持筛选参数
   - 添加URL参数处理
   - 添加滚动时图标状态更新

4. **api/api.php**
   - 修改查询逻辑，支持筛选参数
   - 添加筛选条件的SQL构建

### 新增的文件
1. **test_content_filter.html** - 筛选功能测试页面
2. **内容筛选功能说明.md** - 本说明文档

## 使用示例

### 示例1：按地点筛选
- 输入关键词："西安"
- 筛选结果：只显示用户昵称、用户名、发布地点或动态内容中包含"西安"的动态

### 示例2：按内容筛选
- 输入关键词："美食"
- 筛选结果：只显示动态内容中包含"美食"的动态

### 示例3：按用户筛选
- 输入关键词："张三"
- 筛选结果：只显示用户昵称或用户名中包含"张三"的动态

## 与搜索功能的区别

| 功能 | 用户搜索 | 内容筛选 |
|------|----------|----------|
| 目标 | 查找特定用户 | 筛选动态内容 |
| 结果 | 用户列表 | 筛选后的动态列表 |
| 范围 | 用户表 | 动态表 |
| 交互 | 弹窗显示结果 | 页面重新加载 |

## 性能优化

1. **索引建议**：在以下字段上添加索引以提升查询性能
   - `ptpname` (用户昵称)
   - `ptpuser` (用户名)
   - `ptpdw` (发布地点)
   - `ptptext` (动态内容，可考虑全文索引)

2. **查询优化**：
   - 使用LIKE查询进行模糊匹配
   - 限制查询结果数量
   - 支持分页加载

## 安全考虑

1. **输入过滤**：使用`addslashes()`和`htmlspecialchars()`防止SQL注入
2. **长度限制**：限制筛选关键词最大长度为50字符
3. **参数验证**：检查筛选参数的有效性

## 兼容性

- 与现有搜索功能完全兼容
- 支持同时使用搜索和筛选
- 不影响其他功能的正常使用
- 支持所有现代浏览器

## 测试方法

1. 打开`test_content_filter.html`进行功能测试
2. 测试不同关键词的筛选效果
3. 测试快捷标签功能
4. 测试筛选状态显示和清除功能
5. 测试与搜索功能的组合使用

## 后续优化建议

1. **高级筛选**：支持多个关键词的组合筛选
2. **筛选历史**：记录用户的筛选历史
3. **筛选统计**：显示筛选结果的统计信息
4. **筛选排序**：支持按时间、热度等排序筛选结果
5. **筛选导出**：支持导出筛选结果

---

**开发者**: AI Assistant  
**更新时间**: 2024年  
**版本**: v1.0
