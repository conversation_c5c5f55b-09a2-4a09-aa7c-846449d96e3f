<?php

//decode by nige112
$iteace = "1";
if (is_file("../config.php")) {
	include "../config.php";
}
include "../api/wz.php";
if ($userdlzt == 0) {
	header("location: ./login.php");
	exit;
}
if ($user_zh == $glyadmin) {
	if ($user_passid != $passid) {
		exit("<script language=\"JavaScript\">;alert(\"账号信息异常,请重新登录!\");location.href=\"./login.php\";</script>;");
	}
} else {
	exit("<script language=\"JavaScript\">;alert(\"没有权限!\");location.href=\"../index.php\";</script>;");
}
$Query = "Select count(*) as AllNum from user";
$a = mysqli_query($conn, $Query);
$b = mysqli_fetch_assoc($a);
$usercount = $b["AllNum"];
$Query = "Select count(*) as AllNum from essay";
$a = mysqli_query($conn, $Query);
$b = mysqli_fetch_assoc($a);
$essaycount = $b["AllNum"];
$Query = "Select count(*) as AllNum from comm";
$a = mysqli_query($conn, $Query);
$b = mysqli_fetch_assoc($a);
$commcount = $b["AllNum"];
?><!DOCTYPE html>
<html lang="zh">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0, minimal-ui">
<meta name="keywords" content="<?php echo $name;?>">
<meta name="description" content="<?php echo $name . " ," . $subtitle;?>">
<meta name="author" content="<?php echo $name;?>">
<title>后台首页 - <?php echo $name;?></title>
<link rel="shortcut icon" type="image/x-icon" href="<?php 
if (strpos($icon, "http") !== false) {
	echo $icon;
} else {
	echo "." . $icon;
}
?>">
<meta name="apple-mobile-web-app-capable" content="yes">
<meta name="apple-touch-fullscreen" content="yes">
<meta name="apple-mobile-web-app-status-bar-style" content="default">
<meta name="format-detection" content="telephone=no">
<meta http-equiv="x-rim-auto-match" content="none">
<link rel="stylesheet" type="text/css" href="./assets/css/materialdesignicons.min.css">
<link rel="stylesheet" type="text/css" href="./assets/css/bootstrap.min.css">
<link rel="stylesheet" type="text/css" href="./assets/css/animate.min.css">
<link rel="stylesheet" type="text/css" href="./assets/css/style.min.css">
</head>

<body>
<!--页面loading-->
<div id="lyear-preloader" class="loading">
  <div class="ctn-preloader">
    <div class="round_spinner">
      <div class="spinner"></div>
      <img src="<?php 
if (strpos($logo, "http") !== false) {
	echo $logo;
} else {
	echo "." . $logo;
}
?>" alt="">
    </div>
  </div>
</div>
<!--页面loading end-->
<div class="lyear-layout-web">
  <div class="lyear-layout-container">
    <!--左侧导航-->
    <aside class="lyear-layout-sidebar">
      
      <!-- logo -->
      <div id="logo" class="sidebar-header">
        <a href="./index.php"><img src="<?php 
if (strpos($logo, "http") !== false) {
	echo $logo;
} else {
	echo "." . $logo;
}
?>" title="<?php echo $name;?>" alt="<?php echo $name;?>" /></a>
      </div>
      <div class="lyear-layout-sidebar-info lyear-scroll"> 
        
        <nav class="sidebar-main">
          <ul class="nav-drawer">
            <li class="nav-item active"> <a href="index.php"><i class="mdi mdi-home"></i> <span>后台首页</span></a> </li>
            <li class="nav-item nav-item-has-subnav">
              <a href="javascript:void(0)"><i class="mdi mdi-wan"></i> <span>网站设置</span></a>
              <ul class="nav nav-subnav">
                <li> <a href="./basic.php">基础设置</a> </li>
                <li> <a href="./authority.php">权限设置</a> </li>
                <li> <a href="./imgset.php">图像设置</a> </li>
                <li> <a href="./emailset.php">邮箱配置</a> </li>
              </ul>
            </li>
            <li class="nav-item nav-item-has-subnav">
              <a href="javascript:void(0)"><i class="mdi mdi-account-multiple"></i> <span>用户管理</span></a>
              <ul class="nav nav-subnav">
                <li> <a href="./userlist.php">用户列表</a> </li>
              </ul>
            </li>
            <li class="nav-item nav-item-has-subnav">
              <a href="javascript:void(0)"><i class="mdi mdi-stamper"></i> <span>审核中心</span>
              <?php 
$Query = "Select count(*) as AllNum from essay WHERE ptpaud='0'";
$aes = mysqli_query($conn, $Query);
$escount = mysqli_fetch_assoc($aes);
$essl = $escount["AllNum"];
$Query = "Select count(*) as AllNum from comm WHERE comaud='0'";
$aco = mysqli_query($conn, $Query);
$cocount = mysqli_fetch_assoc($aco);
$cosl = $cocount["AllNum"];
$dshzl = $essl + $cosl;
echo "              ";
if ($dshzl != 0 && $dshzl != "") {
	echo "<span class=\"badge badge-danger\" style=\"margin-left: 10px;\">" . $dshzl . "</span>";
}
?>              </a>
              <ul class="nav nav-subnav">
                <li> <a href="./audites.php">审核文章<?php 
if ($essl != 0 && $essl != "") {
	echo "<span class=\"badge badge-danger\" style=\"margin-left: 10px;\">" . $essl . "</span>";
}
?></a></li>
                <li> <a href="./auditco.php">审核评论<?php 
if ($cosl != 0 && $cosl != "") {
	echo "<span class=\"badge badge-danger\" style=\"margin-left: 10px;\">" . $cosl . "</span>";
}
?></a></li>
              </ul>
            </li>
            <li class="nav-item nav-item-has-subnav">
              <a href="javascript:void(0)"><i class="mdi mdi-link"></i> <span>友链管理</span></a>
              <ul class="nav nav-subnav">
                    <li> <a href="./linkset.php">友链列表</a> </li>
              </ul>
            </li>
            <li class="nav-item nav-item-has-subnav">
              <a href="javascript:void(0)"><i class="mdi mdi-folder-open-outline"></i> <span>资源管理</span></a>
              <ul class="nav nav-subnav">
                <li> <a href="./rm.php">资源列表</a> </li>
                <!--li> <a href="./rmnew.php">新增资源</a> </li-->
              </ul>
            </li>
          </ul>
        </nav>
        
        <div class="sidebar-footer">
          <a href="">刀客源码 © 版权所有 <a href="https://www.dkewl.com/" target="_blank" rel="nofllow"></a>
        </div>
      </div>
      
    </aside>
    <!--End 左侧导航-->
    
    <!--头部信息-->
    <header class="lyear-layout-header">
      
      <nav class="navbar">
      
        <div class="navbar-left">
          <div class="lyear-aside-toggler">
            <span class="lyear-toggler-bar"></span>
            <span class="lyear-toggler-bar"></span>
            <span class="lyear-toggler-bar"></span>
          </div>
        </div>
        
        <ul class="navbar-right d-flex align-items-center">
            <li onclick='window.location.href = "../edit.php"'>
                <span class="icon-item"><i class="mdi mdi-pencil-box-outline"></i></span>
		    </li>
		  
          <!--切换主题配色-->
		  <li class="dropdown dropdown-skin">
		    <span data-toggle="dropdown" class="icon-item"><i class="mdi mdi-palette"></i></span>
			<ul class="dropdown-menu dropdown-menu-right" data-stopPropagation="true">
              <li class="drop-title"><p>主题</p></li>
              <li class="drop-skin-li clearfix">
                <span class="inverse">
                  <input type="radio" name="site_theme" value="default" id="site_theme_1" checked>
                  <label for="site_theme_1"></label>
                </span>
                <span>
                  <input type="radio" name="site_theme" value="dark" id="site_theme_2">
                  <label for="site_theme_2"></label>
                </span>
                <span>
                  <input type="radio" name="site_theme" value="translucent" id="site_theme_3">
                  <label for="site_theme_3"></label>
                </span>
              </li>
			  <li class="drop-title"><p>LOGO</p></li>
			  <li class="drop-skin-li clearfix">
                <span class="inverse">
                  <input type="radio" name="logo_bg" value="default" id="logo_bg_1" checked>
                  <label for="logo_bg_1"></label>
                </span>
                <span>
                  <input type="radio" name="logo_bg" value="color_2" id="logo_bg_2">
                  <label for="logo_bg_2"></label>
                </span>
                <span>
                  <input type="radio" name="logo_bg" value="color_3" id="logo_bg_3">
                  <label for="logo_bg_3"></label>
                </span>
                <span>
                  <input type="radio" name="logo_bg" value="color_4" id="logo_bg_4">
                  <label for="logo_bg_4"></label>
                </span>
                <span>
                  <input type="radio" name="logo_bg" value="color_5" id="logo_bg_5">
                  <label for="logo_bg_5"></label>
                </span>
                <span>
                  <input type="radio" name="logo_bg" value="color_6" id="logo_bg_6">
                  <label for="logo_bg_6"></label>
                </span>
                <span>
                  <input type="radio" name="logo_bg" value="color_7" id="logo_bg_7">
                  <label for="logo_bg_7"></label>
                </span>
                <span>
                  <input type="radio" name="logo_bg" value="color_8" id="logo_bg_8">
                  <label for="logo_bg_8"></label>
                </span>
			  </li>
			  <li class="drop-title"><p>头部</p></li>
			  <li class="drop-skin-li clearfix">
                <span class="inverse">
                  <input type="radio" name="header_bg" value="default" id="header_bg_1" checked>
                  <label for="header_bg_1"></label>                      
                </span>                                                    
                <span>                                                     
                  <input type="radio" name="header_bg" value="color_2" id="header_bg_2">
                  <label for="header_bg_2"></label>                      
                </span>                                                    
                <span>                                                     
                  <input type="radio" name="header_bg" value="color_3" id="header_bg_3">
                  <label for="header_bg_3"></label>
                </span>
                <span>
                  <input type="radio" name="header_bg" value="color_4" id="header_bg_4">
                  <label for="header_bg_4"></label>                      
                </span>                                                    
                <span>                                                     
                  <input type="radio" name="header_bg" value="color_5" id="header_bg_5">
                  <label for="header_bg_5"></label>                      
                </span>                                                    
                <span>                                                     
                  <input type="radio" name="header_bg" value="color_6" id="header_bg_6">
                  <label for="header_bg_6"></label>                      
                </span>                                                    
                <span>                                                     
                  <input type="radio" name="header_bg" value="color_7" id="header_bg_7">
                  <label for="header_bg_7"></label>
                </span>
                <span>
                  <input type="radio" name="header_bg" value="color_8" id="header_bg_8">
                  <label for="header_bg_8"></label>
                </span>
				</li>
			  <li class="drop-title"><p>侧边栏</p></li>
			  <li class="drop-skin-li clearfix">
                <span class="inverse">
                  <input type="radio" name="sidebar_bg" value="default" id="sidebar_bg_1" checked>
                  <label for="sidebar_bg_1"></label>
                </span>
                <span>
                  <input type="radio" name="sidebar_bg" value="color_2" id="sidebar_bg_2">
                  <label for="sidebar_bg_2"></label>
                </span>
                <span>
                  <input type="radio" name="sidebar_bg" value="color_3" id="sidebar_bg_3">
                  <label for="sidebar_bg_3"></label>
                </span>
                <span>
                  <input type="radio" name="sidebar_bg" value="color_4" id="sidebar_bg_4">
                  <label for="sidebar_bg_4"></label>
                </span>
                <span>
                  <input type="radio" name="sidebar_bg" value="color_5" id="sidebar_bg_5">
                  <label for="sidebar_bg_5"></label>
                </span>
                <span>
                  <input type="radio" name="sidebar_bg" value="color_6" id="sidebar_bg_6">
                  <label for="sidebar_bg_6"></label>
                </span>
                <span>
                  <input type="radio" name="sidebar_bg" value="color_7" id="sidebar_bg_7">
                  <label for="sidebar_bg_7"></label>
                </span>
                <span>
                  <input type="radio" name="sidebar_bg" value="color_8" id="sidebar_bg_8">
                  <label for="sidebar_bg_8"></label>
                </span>
			  </li>
		    </ul>
		  </li>
		  
          <!--切换主题配色-->
          <li class="dropdown dropdown-profile">
            <a href="javascript:void(0)" data-toggle="dropdown" class="dropdown-toggle">
              <img class="img-avatar img-avatar-48 m-r-10" src="<?php 
if (strpos($user_img, "http") !== false) {
	echo $user_img;
} else {
	echo "." . $user_img;
}
?>" alt="头像"/>
              <span><?php echo $user_name;?></span>
            </a>
            <ul class="dropdown-menu dropdown-menu-right">
              <li>
                <a class="dropdown-item" href="../index.php"><i class="mdi mdi-home-export-outline"></i> 回到首页</a>
              </li>
              <li class="dropdown-divider"></li>
              <li>
                <a class="dropdown-item" href="JavaScript:;" onclick="logut()"><i class="mdi mdi-logout-variant"></i> 退出登录</a>
              </li>
            </ul>
          </li>
        </ul>
        
      </nav>
      
    </header>
    <!--End 头部信息-->
    
    <!--页面主要内容-->
    <main class="lyear-layout-content">
      <div class="container-fluid p-t-15">
        <div class="row">
          <div class="col-md-6 col-xl-3">
            <div class="card bg-primary text-white">
              <div class="card-body clearfix">
                <div class="flex-box">
                  <span class="img-avatar img-avatar-48 bg-translucent"><i class="mdi mdi-account fs-22"></i></span>
                  <span class="fs-22 lh-22"><?php echo $usercount;?></span>
                </div>
                <div class="text-right">用户总数</div>
              </div>
            </div>
          </div>
          
          <div class="col-md-6 col-xl-3">
            <div class="card bg-danger text-white">
              <div class="card-body clearfix">
                <div class="flex-box">
                  <span class="img-avatar img-avatar-48 bg-translucent"><i class="mdi mdi-script-text fs-22"></i></span>
                  <span class="fs-22 lh-22"><?php echo $essaycount;?></span>
                </div>
                <div class="text-right">文章总数</div>
              </div>
            </div>
          </div>
          
          <div class="col-md-6 col-xl-3">
            <div class="card bg-success text-white">
              <div class="card-body clearfix">
                <div class="flex-box">
                  <span class="img-avatar img-avatar-48 bg-translucent"><i class="mdi mdi-comment-multiple fs-22"></i></span>
                  <span class="fs-22 lh-22"><?php echo $commcount;?></span>
                </div>
                <div class="text-right">评论总数</div>
              </div>
            </div>
          </div>
          
          <div class="col-md-6 col-xl-3">
            <div class="card bg-purple text-white">
              <div class="card-body clearfix">
                <div class="flex-box">
                  <span class="img-avatar img-avatar-48 bg-translucent"><i class="mdi mdi-cloud-check fs-22"></i></span>
                  <span class="fs-22 lh-22">
                  正常                  </span>
                </div>
                <div class="text-right">授权状态</div>
              </div>
            </div>
          </div>
        </div>

        
        
        
              <?php 
if (defined("auth_bdyz")) {
	$valuelixauth = constant("auth_bdyz");
	if ($valuelixauth == "lixianauth") {
		?><div class="row">
                            <div class="col-md-12 col-lg-12">
                            <div class="card">
                            <div class="callout callout-danger"><?php 
		$formattedDate = substr($auth_keytm, 0, 4) . "-" . substr($auth_keytm, 4, 2) . "-" . substr($auth_keytm, 6, 2);
		echo "授权验证失败，由于检测到您在此之前已经授权，现已启动离线授权，您的域名[<span style=\"color: #15c377;\">" . $_SERVER["HTTP_HOST"] . "</span>]离线授权将会在<span style=\"color: #e83e8c;\">" . $formattedDate . "</span>过期";
		?></div>
                            </div>
                          </div>
                        </div><?php 
	}
}
?>              
        
        
        <div class="row">
          <div class="col-md-12 col-lg-12">
              
              
              <div class="card">
              <header class="card-header">
                <div class="card-title">更新日志(<?php echo "当前版本:" . $vernum;?>)</div>
                <ul class="card-actions">
                  <li><a href="#!" class="card-btn-close"><i class="mdi mdi-close"></i></a></li>
                  <li><a href="#!" class="card-btn-slide rotate-180"><i class="mdi mdi-chevron-up"></i></a></li>
                </ul>
              </header>
              <div class="card-body" style="display: none;">
                
                <ul class="lyear-timeline lyear-timeline-left">
                    <?php 
$url = "http://www.qemao.com/files/lan/api/uplog.php";
$timeout = 10;
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, $timeout);
$response = curl_exec($ch);
curl_close($ch);
if ($response === false) {
	$error = curl_error($ch);
	echo "Error: " . $error;
} else {
	$arrbblog = json_decode($response, true);
	$arrbblog = array_reverse($arrbblog);
	if ($vernum < $vernumx) {
		$tisupxx = "<span style=\"color: #4caf50;margin-left: 5px;\">(当前版本可能小于此版本,请留意最新版本)</span>";
	} else {
		$tisupxx = "";
	}
	for ($i = 0; $i < count($arrbblog); $i++) {
		if ($i != 0) {
			echo "<li class=\"lyear-timeline-item\">
                                            <div class=\"lyear-timeline-item-dot\">
                                              <span class=\"badge badge-muted\"></span>
                                            </div>
                                            <div class=\"lyear-timeline-item-content\">
                                              <p class=\"mb-1\"><strong>版本号：V" . $arrbblog[$i]["bb"] . "</strong></p>
                                              <p class=\"mb-0\">" . $arrbblog[$i]["text"] . "</p>
                                              <p><time class=\"mb-3\">" . $arrbblog[$i]["time"] . "</time></p>
                                            </div>
                                          </li>";
		} else {
			echo "<li class=\"lyear-timeline-item\">
                                            <div class=\"lyear-timeline-item-dot\">
                                              <span class=\"badge badge-success\"></span>
                                            </div>
                                            <div class=\"lyear-timeline-item-content\">
                                              <p class=\"mb-1\"><strong>版本号：V" . $arrbblog[$i]["bb"] . $tisupxx . "</strong></p>
                                              <p class=\"mb-0\">" . $arrbblog[$i]["text"] . "</p>
                                              <p><time class=\"mb-3\">" . $arrbblog[$i]["time"] . "</time></p>
                                            </div>
                                          </li>";
		}
	}
}
?>                </ul>
                
              </div>
              
            </div>
            
            
            
          </div>
        </div>
        
        
        
        <div class="row">
          <div class="col-md-6">
            <div class="card">
              <header class="card-header"><div class="card-title">站内通告</div></header>
              <div class="card-body" style="padding: 0px;min-height: 248px;">
                  <div class="card-body">
                      <?php 
$url = "http://www.qemao.com/files/lan/api/admap.php";
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
curl_setopt($ch, CURLOPT_HEADER, 0);
curl_setopt($ch, CURLOPT_USERAGENT, "Mozilla/5.0 (compatible; MSIE 5.01; Windows NT 5.0)");
curl_setopt($ch, CURLOPT_TIMEOUT, 5);
$output = curl_exec($ch);
curl_close($ch);
$arr = json_decode($output, true);
for ($i = 0; $i < count($arr); $i++) {
	$ggatitle = $arr[$i]["title"];
	$ggamsg = $arr[$i]["msg"];
	$ggadurl = $arr[$i]["url"];
	$ggadimg = $arr[$i]["img"];
	if ($ggadimg == "JavaScript:;") {
		$ggadimg = "";
	} else {
		$ggadimg = "background-image: url(" . $ggadimg . ");background-size: cover;background-position: center;text-shadow: 0 0 5px rgb(0 0 0 / 50%);color: #ffffff;";
	}
	if ($ggadurl == "JavaScript:;") {
		$ggadurl = "";
	} else {
		$ggadurl = "style=\"color: #ffffff;\" target=\"_blank\" href=\"" . $ggadurl . "\"";
	}
	echo "<p style=\"margin-bottom: 0rem;padding: 10px 10px;margin-bottom: 10px;" . $ggadimg . "\" class=\"callout callout-success\"><a " . $ggadurl . "\">" . $ggamsg . "</a></p>";
}
?>                  </div>
              </div>
            </div>
          </div>
          
          
            <div class="col-md-6"> 
            <div class="card">
              <div class="card-header">
                <div class="card-title">服务器配置</div>
              </div>
              <div class="card-body" style="padding: 22px;">
                  <button class="btn btn-outline-primary" style="width: 100%;margin-bottom: 15px;">PHP版本: <?php echo PHP_VERSION;?></button>
                 <button class="btn btn-outline-cyan" style="width: 100%;margin-bottom: 15px;">上传文件最大尺寸: <?php echo ini_get("upload_max_filesize");?></button>
                 <button class="btn btn-outline-cyan" style="width: 100%;margin-bottom: 15px;">POST数据最大尺寸: <?php echo ini_get("post_max_size");?></button>
                <!--button class="btn btn-outline-cyan" style="width: 100%;margin-bottom: 15px;">服务器系统: <-?php echo PHP_OS;?></button-->
                <button class="btn btn-outline-success" style="width: 100%;margin-bottom: 15px;">服务器环境: <?php echo $_SERVER["SERVER_SOFTWARE"];?></button>
                <!--button class="btn btn-outline-info" style="width: 100%;margin-bottom: 15px;">服务器协议: <-?php echo $_SERVER['SERVER_PROTOCOL'];?></button-->
              </div>
            </div>
          </div>
        </div>
        

        
          </div>
          
          
          
          
          
          

        </div>
        
      </div>
      
    </main>
    <!--End 页面主要内容-->
  </div>
</div>

<script type="text/javascript" src="./assets/js/jquery.min.js"></script>
<script type="text/javascript" src="./assets/js/popper.min.js"></script>
<script type="text/javascript" src="./assets/js/bootstrap.min.js"></script>
<script type="text/javascript" src="./assets/js/perfect-scrollbar.min.js"></script>
<script type="text/javascript" src="./assets/js/jquery.cookie.min.js"></script>
<script type="text/javascript" src="./assets/js/main.min.js"></script>
<script type="text/javascript" src="./assets/js/Chart.min.js"></script>
</body>
</html>