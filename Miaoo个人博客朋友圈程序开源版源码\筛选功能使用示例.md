# 用户搜索筛选功能使用示例

## 基本搜索

### 示例1：搜索用户名包含"张"的用户
1. 打开用户搜索弹窗
2. 在搜索框输入"张"
3. 系统会显示所有用户名或昵称包含"张"的用户

### 示例2：搜索昵称包含"小明"的用户
1. 在搜索框输入"小明"
2. 系统会显示所有用户名或昵称包含"小明"的用户

## 地区筛选

### 示例3：搜索西安地区的用户
1. 在搜索框输入任意关键词（如"用户"）
2. 点击"筛选条件"展开筛选面板
3. 在"地区筛选"输入框中输入"西安"
4. 系统会显示所有曾在西安发布过动态的用户

### 示例4：搜索北京地区名字包含"李"的用户
1. 在搜索框输入"李"
2. 在地区筛选中输入"北京"
3. 系统会显示名字包含"李"且在北京发布过动态的用户

## 时间筛选

### 示例5：查找今天注册的新用户
1. 在搜索框输入任意关键词
2. 在"注册时间"下拉框选择"今天"
3. 系统会显示今天注册的用户

### 示例6：查找本月注册的用户
1. 在搜索框输入关键词
2. 在"注册时间"下拉框选择"本月"
3. 系统会显示本月注册的用户

## 排序功能

### 示例7：按昵称字母顺序排列
1. 搜索用户
2. 在"排序方式"下拉框选择"昵称(A-Z)"
3. 搜索结果会按昵称字母顺序排列

### 示例8：查看最早注册的用户
1. 搜索用户
2. 在"排序方式"下拉框选择"注册时间(旧到新)"
3. 最早注册的用户会排在前面

## 组合筛选

### 示例9：查找西安地区本周注册的用户
1. 在搜索框输入关键词
2. 地区筛选输入"西安"
3. 注册时间选择"本周"
4. 系统会显示西安地区本周注册的用户

### 示例10：查找包含"王"字的用户，按注册时间排序
1. 搜索框输入"王"
2. 排序方式选择"注册时间(新到旧)"
3. 系统会显示包含"王"字的用户，最新注册的排在前面

## 清除筛选

### 示例11：重置所有筛选条件
1. 设置了多个筛选条件后
2. 点击"清除筛选"按钮
3. 所有筛选条件会被重置为默认值
4. 如果有搜索关键词，会重新执行搜索

## 实际应用场景

### 场景1：寻找同城朋友
- 搜索关键词：任意
- 地区筛选：输入你的城市名
- 排序：按注册时间(新到旧)

### 场景2：寻找新用户
- 搜索关键词：任意
- 注册时间：今天或本周
- 排序：按注册时间(新到旧)

### 场景3：寻找特定昵称的用户
- 搜索关键词：输入昵称关键字
- 排序：按昵称(A-Z)

### 场景4：寻找活跃用户
- 搜索关键词：任意
- 地区筛选：输入城市（说明有发布动态）
- 排序：按注册时间

## 注意事项

1. **地区筛选原理**：基于用户发布动态时的地理位置信息
2. **搜索关键词必填**：必须输入搜索关键词才能进行筛选
3. **实时搜索**：修改任何筛选条件都会立即重新搜索
4. **结果限制**：最多显示20个用户，按相关度和排序规则排列
5. **防抖处理**：输入关键词时有500ms延迟，避免频繁请求

## 技术说明

- 地区筛选通过查询essay表中的ptpdw字段实现
- 时间筛选基于user表的regtime字段
- 支持多条件组合查询
- 使用预处理语句防止SQL注入
- 前端实现防抖和实时搜索

---

通过这些示例，您可以充分利用用户搜索和筛选功能，快速找到目标用户。
